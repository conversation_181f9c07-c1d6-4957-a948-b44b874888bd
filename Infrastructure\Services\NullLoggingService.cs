using System;
using 工具箱.Core.Interfaces;

namespace 工具箱.Infrastructure.Services
{
    /// <summary>
    /// 空日志服务实现，不执行任何实际的日志记录操作
    /// </summary>
    public class NullLoggingService : ILoggingService, IDisposable
    {
        private bool _disposed = false;

        public void LogInfo(string message, params object[] args)
        {
            // 空实现 - 不记录任何日志
        }

        public void LogWarning(string message, params object[] args)
        {
            // 空实现 - 不记录任何日志
        }

        public void LogError(string message, params object[] args)
        {
            // 空实现 - 不记录任何日志
        }

        public void LogError(Exception exception, string message = null)
        {
            // 空实现 - 不记录任何日志
        }

        public void LogDebug(string message, params object[] args)
        {
            // 空实现 - 不记录任何日志
        }

        public void LogToolExecution(string toolName, TimeSpan duration, bool success, string message = null)
        {
            // 空实现 - 不记录任何日志
        }

        public void LogUserAction(string action, string details = null)
        {
            // 空实现 - 不记录任何日志
        }

        public void ClearLogs()
        {
            // 空实现 - 没有日志需要清理
        }

        public string GetLogFilePath()
        {
            // 返回空路径，表示没有日志文件
            return string.Empty;
        }

        public void SetLogLevel(LogLevel level)
        {
            // 空实现 - 不设置日志级别
        }

        public LogLevel GetLogLevel()
        {
            // 返回默认日志级别
            return LogLevel.None;
        }

        public void SetConsoleLogging(bool enabled)
        {
            // 空实现 - 不启用控制台日志
        }

        public void SetFileLogging(bool enabled)
        {
            // 空实现 - 不启用文件日志
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    // 清理托管资源（空实现）
                }

                _disposed = true;
            }
        }

        ~NullLoggingService()
        {
            Dispose(false);
        }
    }
}