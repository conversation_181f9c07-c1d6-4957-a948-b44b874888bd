using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace 工具箱.Core.Interfaces
{
    /// <summary>
    /// 工具管理器接口，负责工具的注册、发现和执行
    /// </summary>
    public interface IToolManager
    {
        /// <summary>
        /// 获取所有已注册的工具
        /// </summary>
        /// <returns>工具集合</returns>
        IEnumerable<ITool> GetAllTools();

        /// <summary>
        /// 根据分类获取工具
        /// </summary>
        /// <param name="category">工具分类</param>
        /// <returns>指定分类的工具集合</returns>
        IEnumerable<ITool> GetToolsByCategory(ToolCategory category);

        /// <summary>
        /// 根据名称获取工具
        /// </summary>
        /// <param name="name">工具名称</param>
        /// <returns>工具实例，如果不存在则返回null</returns>
        ITool GetToolByName(string name);

        /// <summary>
        /// 注册工具
        /// </summary>
        /// <param name="tool">要注册的工具</param>
        void RegisterTool(ITool tool);

        /// <summary>
        /// 注册工具类型
        /// </summary>
        /// <typeparam name="T">工具类型</typeparam>
        void RegisterTool<T>() where T : ITool, new();

        /// <summary>
        /// 取消注册工具
        /// </summary>
        /// <param name="name">工具名称</param>
        /// <returns>是否成功取消注册</returns>
        bool UnregisterTool(string name);

        /// <summary>
        /// 执行指定工具
        /// </summary>
        /// <param name="toolName">工具名称</param>
        /// <param name="parameters">执行参数</param>
        /// <returns>执行结果</returns>
        Task<ToolResult> ExecuteToolAsync(string toolName, ToolParameters parameters = null);

        /// <summary>
        /// 搜索工具
        /// </summary>
        /// <param name="searchTerm">搜索关键词</param>
        /// <returns>匹配的工具集合</returns>
        IEnumerable<ITool> SearchTools(string searchTerm);

        /// <summary>
        /// 验证所有工具配置
        /// </summary>
        /// <returns>验证结果字典，键为工具名称，值为验证结果</returns>
        Dictionary<string, ValidationResult> ValidateAllTools();

        /// <summary>
        /// 工具执行前事件
        /// </summary>
        event EventHandler<ToolExecutionEventArgs> ToolExecuting;

        /// <summary>
        /// 工具执行后事件
        /// </summary>
        event EventHandler<ToolExecutionEventArgs> ToolExecuted;

        /// <summary>
        /// 工具注册事件
        /// </summary>
        event EventHandler<ToolRegistrationEventArgs> ToolRegistered;

        /// <summary>
        /// 工具取消注册事件
        /// </summary>
        event EventHandler<ToolRegistrationEventArgs> ToolUnregistered;
    }

    /// <summary>
    /// 工具执行事件参数
    /// </summary>
    public class ToolExecutionEventArgs : EventArgs
    {
        public string ToolName { get; set; }
        public ToolParameters Parameters { get; set; }
        public ToolResult Result { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public TimeSpan Duration => EndTime - StartTime;
    }

    /// <summary>
    /// 工具注册事件参数
    /// </summary>
    public class ToolRegistrationEventArgs : EventArgs
    {
        public string ToolName { get; set; }
        public ToolCategory Category { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }
}
