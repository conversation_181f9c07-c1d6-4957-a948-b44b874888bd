﻿using System;
using System.Windows.Forms;
using 工具箱.Presentation.Services;

namespace 工具箱
{
    internal static class Program
    {
        private static ApplicationService _applicationService;

        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        [STAThread]
        static void Main()
        {
            try
            {
                // 设置应用程序基本配置
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                Application.SetUnhandledExceptionMode(UnhandledExceptionMode.CatchException);

                // 初始化应用程序服务
                _applicationService = new ApplicationService();

                // 记录应用程序启动
                _applicationService.Logger.LogInfo("应用程序启动");

                // 创建并运行主窗体
                using (var mainForm = new Form1(_applicationService))
                {
                    Application.Run(mainForm);
                }
            }
            catch (Exception ex)
            {
                // 处理启动异常
                HandleStartupException(ex);
            }
            finally
            {
                // 应用程序关闭清理
                try
                {
                    _applicationService?.Shutdown();
                }
                catch (Exception ex)
                {
                    // 记录关闭异常，但不阻止程序退出
                    MessageBox.Show(
                        $"应用程序关闭时发生错误：\n{ex.Message}",
                        "关闭错误",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Warning);
                }
            }
        }

        /// <summary>
        /// 处理启动异常
        /// </summary>
        /// <param name="ex">启动异常</param>
        private static void HandleStartupException(Exception ex)
        {
            var errorMessage = $"应用程序启动失败：\n\n{ex.Message}";

            if (ex.InnerException != null)
            {
                errorMessage += $"\n\n内部异常：{ex.InnerException.Message}";
            }

            errorMessage += "\n\n请尝试以下解决方案：\n";
            errorMessage += "1. 以管理员身份运行程序\n";
            errorMessage += "2. 检查程序文件是否完整\n";
            errorMessage += "3. 重新安装程序\n";
            errorMessage += "4. 联系技术支持";

            MessageBox.Show(
                errorMessage,
                "启动错误",
                MessageBoxButtons.OK,
                MessageBoxIcon.Error);

            // 尝试记录启动异常（如果可能）
            try
            {
                _applicationService?.Logger?.LogError(ex, "应用程序启动失败");
            }
            catch
            {
                // 忽略日志记录失败
            }
        }

        /// <summary>
        /// 获取应用程序服务实例
        /// </summary>
        public static ApplicationService ApplicationService => _applicationService;
    }
}
