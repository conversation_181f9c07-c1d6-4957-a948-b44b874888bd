using System;
using System.Threading.Tasks;

namespace 工具箱.Core.Interfaces
{
    /// <summary>
    /// 工具接口，定义所有工具的基本契约
    /// </summary>
    public interface ITool
    {
        /// <summary>
        /// 工具名称
        /// </summary>
        string Name { get; }

        /// <summary>
        /// 工具描述
        /// </summary>
        string Description { get; }

        /// <summary>
        /// 工具分类
        /// </summary>
        ToolCategory Category { get; }

        /// <summary>
        /// 工具图标（可选）
        /// </summary>
        string Icon { get; }

        /// <summary>
        /// 是否启用
        /// </summary>
        bool IsEnabled { get; }

        /// <summary>
        /// 检查工具是否可以执行
        /// </summary>
        /// <param name="parameters">执行参数</param>
        /// <returns>是否可以执行</returns>
        bool CanExecute(ToolParameters parameters = null);

        /// <summary>
        /// 异步执行工具
        /// </summary>
        /// <param name="parameters">执行参数</param>
        /// <returns>执行结果</returns>
        Task<ToolResult> ExecuteAsync(ToolParameters parameters = null);

        /// <summary>
        /// 验证工具配置
        /// </summary>
        /// <returns>验证结果</returns>
        ValidationResult ValidateConfiguration();
    }

    /// <summary>
    /// 工具分类枚举
    /// </summary>
    public enum ToolCategory
    {
        AI工具,
        系统工具,
        网络工具,
        实用工具,
        其他
    }

    /// <summary>
    /// 工具执行参数
    /// </summary>
    public class ToolParameters
    {
        public object[] Arguments { get; set; } = new object[0];
        public string Context { get; set; }
        public bool SilentMode { get; set; } = false;
    }

    /// <summary>
    /// 工具执行结果
    /// </summary>
    public class ToolResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public object Data { get; set; }
        public Exception Exception { get; set; }
        public TimeSpan ExecutionTime { get; set; }

        public static ToolResult CreateSuccess(string message = null, object data = null)
        {
            return new ToolResult
            {
                Success = true,
                Message = message,
                Data = data
            };
        }

        public static ToolResult CreateFailure(string message, Exception exception = null)
        {
            return new ToolResult
            {
                Success = false,
                Message = message,
                Exception = exception
            };
        }
    }

    /// <summary>
    /// 验证结果
    /// </summary>
    public class ValidationResult
    {
        public bool IsValid { get; set; }
        public string[] Errors { get; set; } = new string[0];
        public string[] Warnings { get; set; } = new string[0];

        public static ValidationResult Valid()
        {
            return new ValidationResult { IsValid = true };
        }

        public static ValidationResult Invalid(params string[] errors)
        {
            return new ValidationResult
            {
                IsValid = false,
                Errors = errors
            };
        }
    }
}
