using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using 工具箱.Business.Tools.SystemTools;

namespace 工具箱
{
    public class SystemCleaner
    {
        public event Action<string> OnFileDeleted;
        public event Action<int, int> OnProgressChanged;
        public event Action<long> OnCleaningComplete;

        // 添加安全限制常量
        private const int MAX_FILES_PER_BATCH = 5000;  // 单批次最大文件数
        private const int MAX_TOTAL_FILES = 50000;     // 总文件数限制
        private const long MAX_MEMORY_USAGE = 500 * 1024 * 1024; // 500MB内存限制

        public async Task CleanAsync()
        {
            long totalBytesCleaned = 0;
            var tempPaths = new List<string>
            {
                Path.GetTempPath(),
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Windows), "Temp")
            };

            try
            {
                int totalFilesProcessed = 0;
                int totalFilesFound = 0;

                // 预检查文件数量，避免处理过多文件
                int estimatedFileCount = await EstimateFileCountAsync(tempPaths);
                if (estimatedFileCount > MAX_TOTAL_FILES)
                {
                    // 如果文件数量过多，使用分批处理
                    var batchResult = await CleanInBatchesAsync(tempPaths);
                    OnCleaningComplete?.Invoke(batchResult.BytesCleared);
                    return;
                }

                // 使用流式处理避免内存溢出
                foreach (var path in tempPaths)
                {
                    if (Directory.Exists(path))
                    {
                        var result = await ProcessDirectoryAsync(path, totalFilesProcessed);

                        // 安全地累加结果，检查溢出
                        if (long.MaxValue - totalBytesCleaned < result.BytesCleared)
                        {
                            throw new OverflowException("清理的字节数超出了系统限制");
                        }

                        totalBytesCleaned += result.BytesCleared;
                        totalFilesProcessed += result.FilesProcessed;
                        totalFilesFound += result.FilesFound;

                        // 检查内存使用情况
                        GC.Collect();
                        var memoryUsage = GC.GetTotalMemory(false);
                        if (memoryUsage > MAX_MEMORY_USAGE)
                        {
                            throw new OutOfMemoryException($"内存使用量 ({memoryUsage / 1024 / 1024}MB) 超出限制");
                        }
                    }
                }

                OnCleaningComplete?.Invoke(totalBytesCleaned);
            }
            catch (ArgumentOutOfRangeException ex)
            {
                throw new InvalidOperationException(
                    "系统文件清理失败：文件数量超出处理能力范围。建议分批清理或联系技术支持。", ex);
            }
            catch (OutOfMemoryException ex)
            {
                throw new InvalidOperationException(
                    "系统文件清理失败：内存不足。建议关闭其他程序后重试或联系技术支持。", ex);
            }
            catch (OverflowException ex)
            {
                throw new InvalidOperationException(
                    "系统文件清理失败：数据量超出系统限制。", ex);
            }
        }

        /// <summary>
        /// 分批清理大量文件
        /// </summary>
        private async Task<CleanupResult> CleanInBatchesAsync(List<string> tempPaths)
        {
            long totalBytesCleaned = 0;
            int totalFilesProcessed = 0;
            int totalFilesFound = 0;
            int batchNumber = 1;

            foreach (var path in tempPaths)
            {
                if (!Directory.Exists(path)) continue;

                try
                {
                    // 分批处理每个目录
                    var batches = await GetDirectoryBatchesAsync(path);

                    foreach (var batch in batches)
                    {
                        OnProgressChanged?.Invoke(totalFilesProcessed, totalFilesFound + batch.Count);

                        var batchResult = await ProcessFileBatchAsync(batch, batchNumber);

                        totalBytesCleaned += batchResult.BytesCleared;
                        totalFilesProcessed += batchResult.FilesProcessed;
                        totalFilesFound += batchResult.FilesFound;

                        batchNumber++;

                        // 每批次后进行垃圾回收
                        GC.Collect();
                        await Task.Delay(100); // 给系统一些恢复时间

                        // 检查内存使用
                        var memoryUsage = GC.GetTotalMemory(false);
                        if (memoryUsage > MAX_MEMORY_USAGE)
                        {
                            await Task.Delay(1000); // 等待更长时间让内存释放
                            GC.Collect();
                        }
                    }
                }
                catch (Exception ex)
                {
                    // 记录错误但继续处理其他目录
                    OnFileDeleted?.Invoke($"处理目录 {path} 时出错: {ex.Message}");
                }
            }

            return new CleanupResult
            {
                FilesDeleted = totalFilesProcessed,
                BytesCleared = totalBytesCleaned,
                FilesFound = totalFilesFound,
                FilesProcessed = totalFilesProcessed
            };
        }

        /// <summary>
        /// 预估指定路径下的文件数量
        /// </summary>
        private async Task<int> EstimateFileCountAsync(List<string> paths)
        {
            int totalCount = 0;

            foreach (var path in paths)
            {
                if (Directory.Exists(path))
                {
                    try
                    {
                        // 只计算第一层文件数量作为估算，避免深度遍历
                        var files = Directory.EnumerateFiles(path, "*", SearchOption.TopDirectoryOnly);
                        var directories = Directory.EnumerateDirectories(path);

                        int fileCount = 0;
                        foreach (var file in files)
                        {
                            fileCount++;
                            if (fileCount > MAX_TOTAL_FILES) return MAX_TOTAL_FILES + 1; // 提前退出
                        }

                        int dirCount = 0;
                        foreach (var dir in directories)
                        {
                            dirCount++;
                            if (dirCount > 100) break; // 限制目录检查数量
                        }

                        // 估算：每个子目录平均50个文件
                        totalCount += fileCount + (dirCount * 50);

                        if (totalCount > MAX_TOTAL_FILES) return totalCount;
                    }
                    catch
                    {
                        // 忽略估算错误，继续处理
                    }
                }

                await Task.Delay(1); // 给UI响应时间
            }

            return totalCount;
        }

        private async Task<CleanupResult> ProcessDirectoryAsync(string directoryPath, int currentFileCount)
        {
            long bytesCleaned = 0;
            int filesProcessed = 0;
            int filesFound = 0;

            try
            {
                // 使用EnumerateFiles进行流式处理，避免一次性加载所有文件
                var files = Directory.EnumerateFiles(directoryPath, "*", SearchOption.AllDirectories);

                foreach (var file in files)
                {
                    // 安全地增加文件计数，检查溢出
                    if (filesFound == int.MaxValue)
                    {
                        throw new OverflowException("文件数量超出整数最大值");
                    }
                    filesFound++;

                    // 检查是否超出单批次限制
                    if (filesProcessed >= MAX_FILES_PER_BATCH)
                    {
                        break; // 达到单批次限制，停止处理
                    }

                    try
                    {
                        var fileInfo = new FileInfo(file);
                        long fileSize = fileInfo.Length;

                        // 检查字节数累加是否会溢出
                        if (long.MaxValue - bytesCleaned < fileSize)
                        {
                            throw new OverflowException("清理的字节数超出系统限制");
                        }

                        fileInfo.Delete();
                        bytesCleaned += fileSize;
                        filesProcessed++;
                        OnFileDeleted?.Invoke(file);
                    }
                    catch (Exception)
                    {
                        // 忽略无法删除的文件 (可能被占用)
                    }

                    // 安全地更新进度，检查溢出
                    try
                    {
                        int currentProgress = currentFileCount + filesProcessed;
                        int totalProgress = filesFound + currentFileCount;

                        // 检查进度计算是否会溢出
                        if (currentFileCount > int.MaxValue - filesProcessed ||
                            filesFound > int.MaxValue - currentFileCount)
                        {
                            // 使用安全的进度报告
                            OnProgressChanged?.Invoke(filesProcessed, filesFound);
                        }
                        else
                        {
                            OnProgressChanged?.Invoke(currentProgress, totalProgress);
                        }
                    }
                    catch (OverflowException)
                    {
                        // 如果进度计算溢出，使用简化的进度报告
                        OnProgressChanged?.Invoke(filesProcessed, filesFound);
                    }

                    // 给UI响应时间，每处理10个文件暂停一次
                    if (filesProcessed % 10 == 0)
                    {
                        await Task.Delay(1);
                    }
                }
            }
            catch (UnauthorizedAccessException)
            {
                // 忽略无权限访问的目录
            }
            catch (DirectoryNotFoundException)
            {
                // 忽略不存在的目录
            }
            catch (ArgumentOutOfRangeException ex)
            {
                // 捕获并重新抛出为更具体的异常
                throw new InvalidOperationException(
                    $"处理目录 {directoryPath} 时发生容量错误：{ex.Message}", ex);
            }

            return new CleanupResult
            {
                FilesDeleted = filesProcessed,
                BytesCleared = bytesCleaned,
                FilesFound = filesFound,
                FilesProcessed = filesProcessed
            };
        }

        /// <summary>
        /// 将目录中的文件分批处理
        /// </summary>
        private async Task<List<List<string>>> GetDirectoryBatchesAsync(string directoryPath)
        {
            var batches = new List<List<string>>();
            var currentBatch = new List<string>();

            try
            {
                var files = Directory.EnumerateFiles(directoryPath, "*", SearchOption.AllDirectories);

                foreach (var file in files)
                {
                    currentBatch.Add(file);

                    if (currentBatch.Count >= MAX_FILES_PER_BATCH)
                    {
                        batches.Add(new List<string>(currentBatch));
                        currentBatch.Clear();

                        // 给UI响应时间
                        await Task.Delay(1);
                    }
                }

                // 添加最后一批
                if (currentBatch.Count > 0)
                {
                    batches.Add(currentBatch);
                }
            }
            catch (Exception ex)
            {
                OnFileDeleted?.Invoke($"获取文件批次时出错: {ex.Message}");
            }

            return batches;
        }

        /// <summary>
        /// 处理一批文件
        /// </summary>
        private async Task<CleanupResult> ProcessFileBatchAsync(List<string> fileBatch, int batchNumber)
        {
            long bytesCleaned = 0;
            int filesProcessed = 0;
            int filesFound = fileBatch.Count;

            OnFileDeleted?.Invoke($"开始处理第 {batchNumber} 批，共 {filesFound} 个文件");

            foreach (var file in fileBatch)
            {
                try
                {
                    var fileInfo = new FileInfo(file);
                    long fileSize = fileInfo.Length;

                    // 检查字节数累加是否会溢出
                    if (long.MaxValue - bytesCleaned < fileSize)
                    {
                        OnFileDeleted?.Invoke($"批次 {batchNumber} 数据量过大，停止处理");
                        break;
                    }

                    fileInfo.Delete();
                    bytesCleaned += fileSize;
                    filesProcessed++;

                    // 每10个文件报告一次进度
                    if (filesProcessed % 10 == 0)
                    {
                        OnFileDeleted?.Invoke($"批次 {batchNumber}: 已处理 {filesProcessed}/{filesFound} 个文件");
                        await Task.Delay(1);
                    }
                }
                catch (Exception ex)
                {
                    // 忽略无法删除的文件，但记录错误
                    OnFileDeleted?.Invoke($"无法删除文件 {file}: {ex.Message}");
                }
            }

            OnFileDeleted?.Invoke($"批次 {batchNumber} 完成：删除了 {filesProcessed} 个文件，释放 {bytesCleaned / (1024.0 * 1024.0):F2} MB");

            return new CleanupResult
            {
                FilesDeleted = filesProcessed,
                BytesCleared = bytesCleaned,
                FilesFound = filesFound,
                FilesProcessed = filesProcessed
            };
        }
    }
}