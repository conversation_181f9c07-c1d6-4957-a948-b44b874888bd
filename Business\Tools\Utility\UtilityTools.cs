using System;
using System.Threading.Tasks;
using System.Windows.Forms;
using 工具箱.Core.Interfaces;
using 工具箱.Core.Models;

namespace 工具箱.Business.Tools.Utility
{
    /// <summary>
    /// 文件搜索工具实现
    /// </summary>
    public class FileSearchTool : BaseTool
    {
        public FileSearchTool(ILoggingService logger, IConfigurationService configService)
            : base(logger, configService)
        {
            Icon = "📁";
        }

        public override string Name => "搜索文件";

        public override string Description => "在系统中搜索指定文件";

        public override ToolCategory Category => ToolCategory.实用工具;

        protected override async Task<ToolResult> ExecuteInternal(ToolParameters parameters)
        {
            try
            {
                LogUserAction("OpenFileSearch");

                await Task.Run(() =>
                {
                    // 打开文件搜索窗体
                    using (var searchForm = new FileSearchForm())
                    {
                        searchForm.ShowDialog();
                    }
                });

                return ToolResult.CreateSuccess("文件搜索窗口已打开");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "打开文件搜索窗口失败");
                return ToolResult.CreateFailure($"打开文件搜索窗口失败: {ex.Message}", ex);
            }
        }

        protected override bool CanExecuteInternal(ToolParameters parameters)
        {
            // 检查是否有访问文件系统的权限
            try
            {
                var drives = System.IO.DriveInfo.GetDrives();
                return drives.Length > 0;
            }
            catch
            {
                return false;
            }
        }

        public override ValidationResult ValidateConfiguration()
        {
            try
            {
                var drives = System.IO.DriveInfo.GetDrives();
                if (drives.Length == 0)
                {
                    return ValidationResult.Invalid("无法访问任何驱动器");
                }

                return ValidationResult.Valid();
            }
            catch (Exception ex)
            {
                return ValidationResult.Invalid($"验证文件系统访问权限失败: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// 思维导图工具实现
    /// </summary>
    public class MindMapTool : UrlBaseTool
    {
        public MindMapTool(ILoggingService logger, IConfigurationService configService)
            : base(logger, configService)
        {
            Icon = "🗺️";
        }

        public override string Name => "思维导图";

        public override string Description => "在线思维导图制作工具";

        public override ToolCategory Category => ToolCategory.实用工具;

        protected override string GetUrl()
        {
            return GetConfigValue("Tools.Utility.MindMap.Url", "https://mermaid.shizhuoran.top/");
        }

        public override ValidationResult ValidateConfiguration()
        {
            var baseValidation = base.ValidateConfiguration();
            if (!baseValidation.IsValid)
                return baseValidation;

            var url = GetUrl();
            if (url.Contains("mermaid.shizhuoran.top"))
            {
                return ValidationResult.Valid();
            }

            return ValidationResult.Invalid("思维导图工具URL配置不正确");
        }
    }

    /// <summary>
    /// FineBI商业智能工具实现
    /// </summary>
    public class FineBITool : UrlBaseTool
    {
        public FineBITool(ILoggingService logger, IConfigurationService configService)
            : base(logger, configService)
        {
            Icon = "📈";
        }

        public override string Name => "FineBI商业智能";

        public override string Description => "FineBI商业智能分析平台";

        public override ToolCategory Category => ToolCategory.实用工具;

        protected override string GetUrl()
        {
            return GetConfigValue("Tools.Utility.PrivacyLock.Url", "http://************:37799/webroot/decision/login?origin=1a7fbec8-c32a-41b7-bbb4-acbe8dbf7e21");
        }

        public override ValidationResult ValidateConfiguration()
        {
            var baseValidation = base.ValidateConfiguration();
            if (!baseValidation.IsValid)
                return baseValidation;

            var url = GetUrl();
            if (url.Contains("************"))
            {
                return ValidationResult.Valid();
            }

            return ValidationResult.Invalid("FineBI URL配置不正确");
        }

        protected override bool CanExecuteInternal(ToolParameters parameters)
        {
            // 检查是否在内网环境
            var url = GetUrl();
            if (url.Contains("************"))
            {
                // 可以添加网络连通性检查
                return true;
            }
            return base.CanExecuteInternal(parameters);
        }
    }
}
