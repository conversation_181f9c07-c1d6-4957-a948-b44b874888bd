using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Net.NetworkInformation;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using 工具箱.Core.Interfaces;
using 工具箱.Core.Models;

namespace 工具箱.Business.Tools.SystemTools
{
    /// <summary>
    /// 清理结果类
    /// </summary>
    public class CleanupResult
    {
        public int FilesDeleted { get; set; }
        public long BytesCleared { get; set; }
        public int FilesFound { get; set; }
        public int FilesProcessed { get; set; }
    }

    /// <summary>
    /// 系统清理工具实现
    /// </summary>
    public class SystemCleanerTool : BaseTool
    {
        public SystemCleanerTool(ILoggingService logger, IConfigurationService configService)
            : base(logger, configService)
        {
            Icon = "🧹";
        }

        public override string Name => "释放系统内存";

        public override string Description => "清理系统临时文件，释放内存空间";

        public override ToolCategory Category => ToolCategory.系统工具;

        protected override async Task<ToolResult> ExecuteInternal(ToolParameters parameters)
        {
            try
            {
                LogUserAction("StartCleanup");

                // 显示确认对话框
                var result = MessageBox.Show(
                    "此操作将删除系统临时文件，是否继续？",
                    "确认",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Warning);

                if (result != DialogResult.Yes)
                {
                    return ToolResult.CreateFailure("用户取消了操作");
                }

                // 检查是否使用静默模式
                bool silentMode = parameters?.SilentMode ?? false;

                if (silentMode)
                {
                    // 静默模式：直接执行清理
                    var cleanupResult = await PerformCleanup();
                    return cleanupResult;
                }
                else
                {
                    // 交互模式：显示进度窗口
                    return await ShowCleanupProgressDialog();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "系统清理工具执行失败");
                return ToolResult.CreateFailure($"系统清理失败: {ex.Message}", ex);
            }
        }

        protected override bool CanExecuteInternal(ToolParameters parameters)
        {
            // 检查是否有足够的权限访问临时文件夹
            try
            {
                var tempPath = System.IO.Path.GetTempPath();
                return System.IO.Directory.Exists(tempPath);
            }
            catch
            {
                return false;
            }
        }

        public override ValidationResult ValidateConfiguration()
        {
            try
            {
                var tempPath = System.IO.Path.GetTempPath();
                var windowsTempPath = System.IO.Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Windows), "Temp");

                if (!System.IO.Directory.Exists(tempPath))
                {
                    return ValidationResult.Invalid("无法访问用户临时文件夹");
                }

                if (!System.IO.Directory.Exists(windowsTempPath))
                {
                    return ValidationResult.Invalid("无法访问Windows临时文件夹");
                }

                return ValidationResult.Valid();
            }
            catch (Exception ex)
            {
                return ValidationResult.Invalid($"配置验证失败: {ex.Message}");
            }
        }

        private async Task<ToolResult> PerformCleanup()
        {
            try
            {
                // 添加系统环境诊断
                var diagnosticInfo = GetSystemDiagnosticInfo();
                _logger.LogInfo($"开始系统清理，环境信息：{diagnosticInfo}");

                // 使用新的SystemCleaner进行清理
                var systemCleaner = new SystemCleaner();

                // 设置事件处理器以记录进度和结果
                int processedFiles = 0;
                long totalBytesCleared = 0;
                int maxFilesReported = 0;

                systemCleaner.OnProgressChanged += (current, total) =>
                {
                    processedFiles = current;
                    maxFilesReported = Math.Max(maxFilesReported, total);

                    // 记录进度信息用于诊断
                    if (current % 1000 == 0) // 每1000个文件记录一次
                    {
                        _logger.LogInfo($"清理进度：已处理 {current} 个文件，总计发现 {total} 个文件");
                    }
                };

                systemCleaner.OnCleaningComplete += (bytes) =>
                {
                    totalBytesCleared = bytes;
                };

                await systemCleaner.CleanAsync();

                var sizeInMB = totalBytesCleared / (1024.0 * 1024.0);
                var message = $"清理完成！删除了 {processedFiles} 个文件，释放了 {sizeInMB:F2} MB 空间";

                LogUserAction("CleanupCompleted", $"Files: {processedFiles}, Size: {sizeInMB:F2}MB, MaxFound: {maxFilesReported}");

                return ToolResult.CreateSuccess(message, new
                {
                    FilesDeleted = processedFiles,
                    BytesCleared = totalBytesCleared,
                    SizeMB = sizeInMB,
                    MaxFilesFound = maxFilesReported
                });
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains("文件数量超出处理能力范围"))
            {
                _logger.LogError(ex, "系统文件清理失败：容量超出限制");
                
                MessageBox.Show(
                    "系统文件清理报错，参数错误：容量超出了最大容量\n\n" +
                    "错误原因：临时文件夹中的文件数量过多，超出了系统处理能力范围。\n\n" +
                    "建议解决方案：\n" +
                    "1. 手动删除部分临时文件后重试\n" +
                    "2. 重启计算机后再次尝试清理\n" +
                    "3. 使用系统自带的磁盘清理工具\n" +
                    "4. 联系技术支持获取专业协助\n\n" +
                    "技术详情：当前系统限制单次处理文件数量不超过10,000个",
                    "系统清理失败",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Warning);
                
                return ToolResult.CreateFailure("系统文件清理失败：文件数量超出处理能力范围", ex);
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains("内存不足"))
            {
                _logger.LogError(ex, "系统文件清理失败：内存不足");
                
                MessageBox.Show(
                    "系统文件清理报错，参数错误：内存容量不足\n\n" +
                    "错误原因：系统可用内存不足以完成清理操作。\n\n" +
                    "建议解决方案：\n" +
                    "1. 关闭其他正在运行的程序\n" +
                    "2. 重启计算机释放内存\n" +
                    "3. 检查系统内存使用情况\n" +
                    "4. 联系技术支持检查硬件配置",
                    "系统清理失败",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Warning);
                
                return ToolResult.CreateFailure("系统文件清理失败：内存不足", ex);
            }
            catch (ArgumentOutOfRangeException ex) when (ex.ParamName == "capacity")
            {
                var diagnosticInfo = GetSystemDiagnosticInfo();
                _logger.LogError(ex, $"系统文件清理失败：capacity参数错误。诊断信息：{diagnosticInfo}");

                MessageBox.Show(
                    "系统文件清理报错，参数错误：容量超出了最大容量\n" +
                    "参数名: capacity\n\n" +
                    "错误分析：系统尝试处理的文件数量或数据量超出了.NET框架的限制。\n\n" +
                    "当前系统状态：\n" +
                    $"{diagnosticInfo}\n\n" +
                    "建议解决方案：\n" +
                    "1. 手动删除部分临时文件后重试\n" +
                    "2. 重启系统释放内存后重试\n" +
                    "3. 使用系统自带的磁盘清理工具\n" +
                    "4. 分批清理：先清理用户临时文件夹，再清理系统临时文件夹\n" +
                    "5. 联系技术支持获取专业协助\n\n" +
                    "技术详情：当前系统限制单次处理文件数量不超过50,000个",
                    "系统清理失败",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);

                return ToolResult.CreateFailure("系统文件清理失败：capacity参数错误", ex);
            }
            catch (OverflowException ex)
            {
                var diagnosticInfo = GetSystemDiagnosticInfo();
                _logger.LogError(ex, $"系统文件清理失败：数值溢出。诊断信息：{diagnosticInfo}");

                MessageBox.Show(
                    "系统文件清理报错：数据量超出系统限制\n\n" +
                    "错误原因：临时文件的数量或大小超出了系统能够处理的范围。\n\n" +
                    "当前系统状态：\n" +
                    $"{diagnosticInfo}\n\n" +
                    "建议解决方案：\n" +
                    "1. 手动删除大型临时文件\n" +
                    "2. 分批清理不同的临时文件夹\n" +
                    "3. 重启计算机后重试\n" +
                    "4. 使用专业的磁盘清理工具\n" +
                    "5. 联系技术支持",
                    "系统清理失败",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);

                return ToolResult.CreateFailure("系统文件清理失败：数据量超出系统限制", ex);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "系统文件清理过程中发生未知错误");
                return ToolResult.CreateFailure($"清理过程中发生错误: {ex.Message}", ex);
            }
        }

        private async Task<ToolResult> ShowCleanupProgressDialog()
        {
            try
            {
                ToolResult result = null;

                // 在UI线程中显示进度对话框
                if (Application.OpenForms.Count > 0)
                {
                    var mainForm = Application.OpenForms[0];
                    if (mainForm.InvokeRequired)
                    {
                        await Task.Run(() =>
                        {
                            mainForm.Invoke(new Action(() =>
                            {
                                using (var progressForm = new CleanupProgressForm())
                                {
                                    var dialogResult = progressForm.ShowDialog(mainForm);
                                    result = ToolResult.CreateSuccess("清理完成");
                                }
                            }));
                        });
                    }
                    else
                    {
                        using (var progressForm = new CleanupProgressForm())
                        {
                            var dialogResult = progressForm.ShowDialog(mainForm);
                            result = ToolResult.CreateSuccess("清理完成");
                        }
                    }
                }
                else
                {
                    // 如果没有主窗体，直接显示
                    using (var progressForm = new CleanupProgressForm())
                    {
                        var dialogResult = progressForm.ShowDialog();
                        result = ToolResult.CreateSuccess("清理完成");
                    }
                }

                return result ?? ToolResult.CreateFailure("清理过程被中断");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "显示进度对话框失败");

                // 如果进度对话框失败，回退到静默模式
                MessageBox.Show(
                    "无法显示进度窗口，将使用静默模式进行清理。",
                    "提示",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                return await PerformCleanup();
            }
        }

        /// <summary>
        /// 清理指定目录的静态方法
        /// </summary>
        /// <param name="directoryPath">要清理的目录路径</param>
        /// <returns>清理结果</returns>
        private static CleanupResult CleanDirectoryStatic(string directoryPath)
        {
            int filesDeleted = 0;
            long bytesCleared = 0;

            try
            {
                if (!System.IO.Directory.Exists(directoryPath))
                    return new CleanupResult { FilesDeleted = 0, BytesCleared = 0 };

                var files = System.IO.Directory.GetFiles(directoryPath, "*", System.IO.SearchOption.AllDirectories);

                foreach (var file in files)
                {
                    try
                    {
                        var fileInfo = new System.IO.FileInfo(file);
                        var fileSize = fileInfo.Length;

                        System.IO.File.Delete(file);
                        filesDeleted++;
                        bytesCleared += fileSize;
                    }
                    catch
                    {
                        // 忽略无法删除的文件
                    }
                }
            }
            catch
            {
                // 忽略目录访问错误
            }

            return new CleanupResult { FilesDeleted = filesDeleted, BytesCleared = bytesCleared };
        }

        /// <summary>
        /// 获取系统诊断信息
        /// </summary>
        private string GetSystemDiagnosticInfo()
        {
            try
            {
                var tempPath = Path.GetTempPath();
                var windowsTempPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Windows), "Temp");

                var memoryUsage = GC.GetTotalMemory(false) / (1024 * 1024); // MB
                var availableMemory = GetAvailablePhysicalMemory();

                var tempDirExists = Directory.Exists(tempPath);
                var windowsTempExists = Directory.Exists(windowsTempPath);

                // 快速估算文件数量（只检查第一层）
                int userTempFileCount = 0;
                int systemTempFileCount = 0;

                try
                {
                    if (tempDirExists)
                    {
                        userTempFileCount = Directory.GetFiles(tempPath, "*", SearchOption.TopDirectoryOnly).Length;
                    }
                }
                catch { userTempFileCount = -1; }

                try
                {
                    if (windowsTempExists)
                    {
                        systemTempFileCount = Directory.GetFiles(windowsTempPath, "*", SearchOption.TopDirectoryOnly).Length;
                    }
                }
                catch { systemTempFileCount = -1; }

                return $"内存使用: {memoryUsage}MB, 可用内存: {availableMemory}MB, " +
                       $"用户临时文件: {userTempFileCount}, 系统临时文件: {systemTempFileCount}, " +
                       $"用户临时目录: {(tempDirExists ? "存在" : "不存在")}, " +
                       $"系统临时目录: {(windowsTempExists ? "存在" : "不存在")}";
            }
            catch (Exception ex)
            {
                return $"诊断信息获取失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 获取可用物理内存（MB）
        /// </summary>
        private long GetAvailablePhysicalMemory()
        {
            try
            {
                using (var process = new Process())
                {
                    process.StartInfo.FileName = "wmic";
                    process.StartInfo.Arguments = "OS get TotalVisibleMemorySize /value";
                    process.StartInfo.UseShellExecute = false;
                    process.StartInfo.RedirectStandardOutput = true;
                    process.StartInfo.CreateNoWindow = true;

                    process.Start();
                    var output = process.StandardOutput.ReadToEnd();
                    process.WaitForExit();

                    var lines = output.Split('\n');
                    foreach (var line in lines)
                    {
                        if (line.StartsWith("TotalVisibleMemorySize="))
                        {
                            var value = line.Split('=')[1].Trim();
                            if (long.TryParse(value, out long kb))
                            {
                                return kb / 1024; // 转换为MB
                            }
                        }
                    }
                }
            }
            catch
            {
                // 如果获取失败，返回-1表示未知
            }

            return -1;
        }
    }

    /// <summary>
    /// 修改计算机密码工具实现
    /// </summary>
    public class ChangePasswordTool : BaseTool
    {
        public ChangePasswordTool(ILoggingService logger, IConfigurationService configService)
            : base(logger, configService)
        {
            Icon = "🔐";
        }

        public override string Name => "修改计算机密码";

        public override string Description => "使用PowerShell命令修改计算机密码";

        public override ToolCategory Category => ToolCategory.系统工具;

        protected override async Task<ToolResult> ExecuteInternal(ToolParameters parameters)
        {
            try
            {
                LogUserAction("ChangePasswordWithPowerShell");

                // 显示操作提示
                var result = MessageBox.Show(
                    "即将使用PowerShell命令修改计算机密码。\n\n" +
                    "此功能支持本地用户和域用户密码修改。\n\n" +
                    "请注意：\n" +
                    "• 您需要输入当前密码进行验证\n" +
                    "• 新密码要求：密码长度至少7位\n" +
                    "• 此操作需要管理员权限\n\n" +
                    "是否继续？",
                    "修改计算机密码",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result != DialogResult.Yes)
                {
                    return ToolResult.CreateFailure("用户取消了操作");
                }

                // 获取密码输入
                var passwordInput = GetPasswordInput();
                if (passwordInput == null)
                {
                    return ToolResult.CreateFailure("用户取消了密码输入");
                }

                // 验证新密码强度
                var passwordValidation = ValidatePassword(passwordInput.NewPassword);
                if (!passwordValidation.IsValid)
                {
                    MessageBox.Show(
                        $"密码强度不符合要求：\n{passwordValidation.Message}",
                        "密码验证失败",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Warning);
                    return ToolResult.CreateFailure("密码强度验证失败");
                }

                // 执行密码修改
                var changeResult = await ExecutePasswordChange(passwordInput.CurrentPassword, passwordInput.NewPassword);
                
                return changeResult;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "使用PowerShell修改密码失败");
                return ToolResult.CreateFailure($"密码修改失败：{ex.Message}");
            }
        }

        private class PasswordInput
        {
            public string CurrentPassword { get; set; }
            public string NewPassword { get; set; }
        }

        private class PasswordValidationResult
        {
            public bool IsValid { get; set; }
            public string Message { get; set; }
        }

        /// <summary>
        /// 检测当前用户是否为域用户
        /// </summary>
        /// <returns>如果是域用户返回true，否则返回false</returns>
        private bool IsDomainUser()
        {
            try
            {
                string domainName = Environment.UserDomainName;
                string machineName = Environment.MachineName;
                
                // 如果域名与机器名不同，则为域用户
                return !string.Equals(domainName, machineName, StringComparison.OrdinalIgnoreCase);
            }
            catch
            {
                // 如果无法获取域信息，假设为本地用户
                return false;
            }
        }

        private PasswordValidationResult ValidatePassword(string password)
        {
            var result = new PasswordValidationResult { IsValid = true, Message = "" };
            var issues = new List<string>();

            // 检查密码长度
            if (password.Length < 7)
            {
                issues.Add("密码长度至少7位");
            }

            // 检查是否包含常见弱密码模式
            string[] weakPatterns = { "123456", "password", "admin", "user", "qwerty", "abc123" };
            if (weakPatterns.Any(pattern => password.ToLower().Contains(pattern)))
            {
                issues.Add("不能包含常见的弱密码模式");
            }

            if (issues.Count > 0)
            {
                result.IsValid = false;
                result.Message = "• " + string.Join("\n• ", issues);
            }

            return result;
        }

        private PasswordInput GetPasswordInput()
        {
            using (var form = new Form())
            {
                form.Text = "密码修改";
                form.Size = new Size(400, 250);
                form.StartPosition = FormStartPosition.CenterParent;
                form.FormBorderStyle = FormBorderStyle.FixedDialog;
                form.MaximizeBox = false;
                form.MinimizeBox = false;

                var lblCurrent = new Label()
                {
                    Text = "当前密码:",
                    Location = new Point(20, 20),
                    Size = new Size(80, 23)
                };

                var txtCurrent = new TextBox()
                {
                    Location = new Point(110, 20),
                    Size = new Size(250, 23),
                    UseSystemPasswordChar = true
                };

                var lblNew = new Label()
                {
                    Text = "新密码:",
                    Location = new Point(20, 60),
                    Size = new Size(80, 23)
                };

                var txtNew = new TextBox()
                {
                    Location = new Point(110, 60),
                    Size = new Size(250, 23),
                    UseSystemPasswordChar = true
                };

                var lblConfirm = new Label()
                {
                    Text = "确认密码:",
                    Location = new Point(20, 100),
                    Size = new Size(80, 23)
                };

                var txtConfirm = new TextBox()
                {
                    Location = new Point(110, 100),
                    Size = new Size(250, 23),
                    UseSystemPasswordChar = true
                };

                var btnOK = new Button()
                {
                    Text = "确定",
                    Location = new Point(200, 150),
                    Size = new Size(75, 30),
                    DialogResult = DialogResult.OK
                };

                var btnCancel = new Button()
                {
                    Text = "取消",
                    Location = new Point(285, 150),
                    Size = new Size(75, 30),
                    DialogResult = DialogResult.Cancel
                };

                form.Controls.AddRange(new Control[] { lblCurrent, txtCurrent, lblNew, txtNew, lblConfirm, txtConfirm, btnOK, btnCancel });
                form.AcceptButton = btnOK;
                form.CancelButton = btnCancel;

                btnOK.Click += (s, e) =>
                {
                    if (string.IsNullOrEmpty(txtCurrent.Text))
                    {
                        MessageBox.Show("请输入当前密码", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }

                    if (string.IsNullOrEmpty(txtNew.Text))
                    {
                        MessageBox.Show("请输入新密码", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }

                    if (txtNew.Text != txtConfirm.Text)
                    {
                        MessageBox.Show("新密码和确认密码不匹配", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }

                    form.DialogResult = DialogResult.OK;
                    form.Close();
                };

                if (form.ShowDialog() == DialogResult.OK)
                {
                    return new PasswordInput
                    {
                        CurrentPassword = txtCurrent.Text,
                        NewPassword = txtNew.Text
                    };
                }

                return null;
            }
        }

        private async Task<ToolResult> ExecutePasswordChange(string currentPassword, string newPassword)
        {
            try
            {
                // 获取当前用户名和域信息
                string currentUser = Environment.UserName;
                string domainName = Environment.UserDomainName;
                bool isDomain = IsDomainUser();
                
                // 使用Base64编码密码以避免特殊字符问题
                string currentPasswordBase64 = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(currentPassword));
                string newPasswordBase64 = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(newPassword));
                
                _logger.LogInfo($"开始为{(isDomain ? "域" : "本地")}用户 {currentUser} 执行密码修改操作");
                
                // 根据用户类型构建不同的PowerShell命令
                string script;
                if (isDomain)
                {
                    // 域用户的PowerShell脚本
                    script = $@"
try {{
    Add-Type -AssemblyName System.DirectoryServices.AccountManagement
    
    # 解码密码
    $currentPasswordBytes = [System.Convert]::FromBase64String('{currentPasswordBase64}')
    $currentPassword = [System.Text.Encoding]::UTF8.GetString($currentPasswordBytes)
    
    $newPasswordBytes = [System.Convert]::FromBase64String('{newPasswordBase64}')
    $newPassword = [System.Text.Encoding]::UTF8.GetString($newPasswordBytes)
    
    # 验证当前密码（域用户）
    $pc = New-Object System.DirectoryServices.AccountManagement.PrincipalContext([System.DirectoryServices.AccountManagement.ContextType]::Domain, '{domainName}')
    $isValid = $pc.ValidateCredentials('{currentUser}', $currentPassword)
    
    if (-not $isValid) {{
        Write-Output 'INVALID_CURRENT_PASSWORD'
        exit 1
    }}
    
    # 修改域用户密码
    $user = [System.DirectoryServices.AccountManagement.UserPrincipal]::FindByIdentity($pc, [System.DirectoryServices.AccountManagement.IdentityType]::SamAccountName, '{currentUser}')
    if ($user -eq $null) {{
        Write-Output 'ERROR: 无法找到域用户'
        exit 1
    }}
    
    $user.ChangePassword($currentPassword, $newPassword)
    $user.Save()
    
    Write-Output 'SUCCESS'
    exit 0
}}
catch {{
    $errorMsg = $_.Exception.Message
    if ($errorMsg -like '*拒绝访问*' -or $errorMsg -like '*Access*denied*') {{
        Write-Output ""ERROR: 权限不足，无法修改域用户密码。请确保：1) 当前密码正确 2) 新密码符合域密码策略 3) 账户未被锁定""
    }}
    elseif ($errorMsg -like '*密码*策略*' -or $errorMsg -like '*password*policy*') {{
        Write-Output ""ERROR: 新密码不符合域密码策略要求，请检查密码复杂度、长度和历史记录要求""
    }}
    else {{
        Write-Output ""ERROR: $errorMsg""
    }}
    Write-Error $_.Exception.Message
    exit 1
}}";
                }
                else
                {
                    // 本地用户的PowerShell脚本
                    script = $@"
try {{
    Add-Type -AssemblyName System.DirectoryServices.AccountManagement
    
    # 解码密码
    $currentPasswordBytes = [System.Convert]::FromBase64String('{currentPasswordBase64}')
    $currentPassword = [System.Text.Encoding]::UTF8.GetString($currentPasswordBytes)
    
    $newPasswordBytes = [System.Convert]::FromBase64String('{newPasswordBase64}')
    $newPassword = [System.Text.Encoding]::UTF8.GetString($newPasswordBytes)
    
    # 验证当前密码（本地用户）
    $pc = New-Object System.DirectoryServices.AccountManagement.PrincipalContext([System.DirectoryServices.AccountManagement.ContextType]::Machine)
    $isValid = $pc.ValidateCredentials('{currentUser}', $currentPassword)
    
    if (-not $isValid) {{
        Write-Output 'INVALID_CURRENT_PASSWORD'
        exit 1
    }}
    
    # 修改本地用户密码
    $user = [ADSI]'WinNT://./{currentUser},user'
    $user.SetPassword($newPassword)
    $user.SetInfo()
    
    Write-Output 'SUCCESS'
    exit 0
}}
catch {{
    Write-Output ""ERROR: $($_.Exception.Message)""
    exit 1
}}";
                }

                // 使用Process.Start执行PowerShell命令
                var processInfo = new ProcessStartInfo
                {
                    FileName = "powershell.exe",
                    Arguments = $"-Command \"{script.Replace("\"", "\\\"")}\"",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true,
                    WindowStyle = ProcessWindowStyle.Hidden
                };

                using (var process = new Process { StartInfo = processInfo })
                {
                    process.Start();

                    string output = await process.StandardOutput.ReadToEndAsync();
                    string error = await process.StandardError.ReadToEndAsync();

                    await Task.Run(() => process.WaitForExit());

                    string result = output.Trim();
                    int exitCode = process.ExitCode;

                    _logger.LogInfo($"系统密码修改操作执行完成，退出代码: {exitCode}，返回状态: {result}");

                    // 修复：优先检查退出代码和输出内容，而不是StandardError
                    // StandardError可能包含非致命的警告信息，不应作为失败的唯一判断标准
                    if (exitCode != 0 && !result.Equals("SUCCESS", StringComparison.OrdinalIgnoreCase))
                    {
                        // 只有在退出代码非0且输出不是SUCCESS时才记录错误
                        if (!string.IsNullOrEmpty(error))
                        {
                            _logger.LogError($"PowerShell执行错误: {error}");
                        }
                        _logger.LogError($"PowerShell输出: {output}");

                        // 如果有具体的错误输出，使用它；否则使用通用错误信息
                        string errorMessage = !string.IsNullOrEmpty(error) ? error : "PowerShell命令执行失败";
                        return ToolResult.CreateFailure($"密码修改失败: {errorMessage}");
                    }

                    if (result == "INVALID_CURRENT_PASSWORD")
                    {
                        _logger.LogWarning($"用户身份验证失败 - 用户: {currentUser}，原因: 当前密码验证不通过");
                        MessageBox.Show("身份验证失败\n\n" +
                                      "系统无法验证您提供的当前密码。请确认以下事项：\n\n" +
                                      "• 当前密码输入是否正确\n" +
                                      "• 账户是否处于正常状态\n" +
                                      "• 域控制器连接是否正常\n" +
                                      "• 账户是否被临时锁定\n\n" +
                                      "如问题持续存在，请联系系统管理员。",
                                      "身份验证失败",
                                      MessageBoxButtons.OK,
                                      MessageBoxIcon.Warning);
                        return ToolResult.CreateFailure("用户身份验证失败：当前密码验证不通过");
                    }
                    else if (result == "SUCCESS")
                    {
                        MessageBox.Show("密码修改操作已成功完成\n\n" +
                                      "您的账户密码已更新。为确保安全性，请注意：\n\n" +
                                      "• 立即使用新密码重新登录系统\n" +
                                      "• 更新所有保存的凭据信息\n" +
                                      "• 确保新密码符合安全策略要求",
                                      "操作成功",
                                      MessageBoxButtons.OK,
                                      MessageBoxIcon.Information);
                        return ToolResult.CreateSuccess("系统密码修改操作已成功完成");
                    }
                    else if (result.StartsWith("ERROR:"))
                    {
                        string errorMsg = result.Substring(6).Trim();
                        _logger.LogError($"密码修改操作失败，系统返回错误: {errorMsg}");
                        MessageBox.Show($"操作执行失败\n\n" +
                                      $"系统返回错误信息：\n{errorMsg}\n\n" +
                                      "建议操作：\n" +
                                      "• 检查新密码是否符合安全策略\n" +
                                      "• 确认账户权限是否充足\n" +
                                      "• 验证系统服务运行状态\n" +
                                      "• 如需协助请联系技术支持",
                                      "操作失败",
                                      MessageBoxButtons.OK,
                                      MessageBoxIcon.Error);
                        return ToolResult.CreateFailure($"密码修改操作失败: {errorMsg}");
                    }

                    // 记录详细的调试信息以便排查问题
                    _logger.LogError($"PowerShell命令执行异常，返回了未预期的结果: {result}");
                    _logger.LogError($"退出代码: {exitCode}");
                    if (!string.IsNullOrEmpty(error))
                    {
                        _logger.LogError($"StandardError内容: {error}");
                    }

                    MessageBox.Show($"系统执行异常\n\n" +
                                  $"PowerShell命令返回了未预期的结果：{result}\n" +
                                  $"退出代码：{exitCode}\n\n" +
                                  "可能原因：\n" +
                                  "• PowerShell脚本执行过程中出现未知错误\n" +
                                  "• 系统环境配置异常\n" +
                                  "• 权限或策略限制\n\n" +
                                  "建议操作：\n" +
                                  "• 重新尝试操作\n" +
                                  "• 检查系统日志\n" +
                                  "• 联系技术支持",
                                  "系统执行异常",
                                  MessageBoxButtons.OK,
                                  MessageBoxIcon.Error);

                    return ToolResult.CreateFailure($"系统执行异常：PowerShell命令返回了未预期的结果 (退出代码: {exitCode})");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "PowerShell密码修改命令执行过程中发生异常");
                
                // 提供详细的技术支持信息
                MessageBox.Show($"系统执行异常\n\n" +
                              $"错误详情：{ex.Message}\n\n" +
                              "可能原因及解决方案：\n\n" +
                              "权限问题：\n" +
                              "• 请以管理员身份运行此应用程序\n" +
                              "• 确认当前用户具有修改密码的权限\n\n" +
                              "系统策略限制：\n" +
                              "• 检查PowerShell执行策略设置\n" +
                              "• 验证组策略是否允许密码修改\n\n" +
                              "替代方案：\n" +
                              "• 使用系统快捷键 Ctrl+Alt+Del 进入安全桌面\n" +
                              "• 选择\"更改密码\"选项进行操作\n\n" +
                              "如需技术支持，请联系系统管理员。",
                              "系统执行异常",
                              MessageBoxButtons.OK,
                              MessageBoxIcon.Error);
                
                return ToolResult.CreateFailure($"系统执行异常: {ex.Message}");
            }
        }


    }

    /// <summary>
    /// IP信息查看工具实现
    /// </summary>
    public class IPInfoTool : BaseTool
    {
        public IPInfoTool(ILoggingService logger, IConfigurationService configService)
            : base(logger, configService)
        {
            Icon = "🌐";
        }

        public override string Name => "IP信息查看";

        public override string Description => "查看计算机网络信息和IP地址";

        public override ToolCategory Category => ToolCategory.系统工具;

        protected override async Task<ToolResult> ExecuteInternal(ToolParameters parameters)
        {
            try
            {
                LogUserAction("GetIPInfo");

                var ipInfo = await GetNetworkInformation();

                // 显示信息
                MessageBox.Show(
                    ipInfo,
                    "IP信息查看",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                return ToolResult.CreateSuccess("IP信息获取完成", ipInfo);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取IP信息失败");
                MessageBox.Show(
                    $"获取IP信息时发生错误：\n{ex.Message}",
                    "错误",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);

                return ToolResult.CreateFailure($"获取IP信息失败: {ex.Message}", ex);
            }
        }

        private async Task<string> GetNetworkInformation()
        {
            return await Task.Run(() =>
            {
                var infoBuilder = new System.Text.StringBuilder();

                try
                {
                    // 获取计算机名称
                    string computerName = Environment.MachineName;

                    // 获取用户名
                    string userName = Environment.UserName;

                    // 获取IP地址
                    string ipAddress = GetLocalIPAddress();

                    // 获取域名（如果有）
                    string domainName = Environment.UserDomainName;

                    // 获取操作系统信息
                    string osVersion = Environment.OSVersion.ToString();

                    // 构建信息字符串
                    infoBuilder.AppendLine("=== 计算机网络信息 ===");
                    infoBuilder.AppendLine();
                    infoBuilder.AppendLine($"计算机名称：{computerName}");
                    infoBuilder.AppendLine($"用户名：{userName}");
                    infoBuilder.AppendLine($"域名：{domainName}");
                    infoBuilder.AppendLine($"IP地址：{ipAddress}");
                    infoBuilder.AppendLine($"操作系统：{osVersion}");
                    infoBuilder.AppendLine();
                    infoBuilder.AppendLine("=== 网络适配器信息 ===");

                    // 获取详细的网络适配器信息
                    var networkInfo = GetNetworkAdapterInfo();
                    infoBuilder.AppendLine(networkInfo);
                }
                catch (Exception ex)
                {
                    infoBuilder.AppendLine($"获取网络信息时发生错误：{ex.Message}");
                }

                return infoBuilder.ToString();
            });
        }

        private string GetLocalIPAddress()
        {
            try
            {
                var host = System.Net.Dns.GetHostEntry(System.Net.Dns.GetHostName());
                foreach (var ip in host.AddressList)
                {
                    if (ip.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork)
                    {
                        return ip.ToString();
                    }
                }
                return "未找到IPv4地址";
            }
            catch
            {
                return "获取IP地址失败";
            }
        }

        private string GetNetworkAdapterInfo()
        {
            try
            {
                var info = new System.Text.StringBuilder();
                var networkInterfaces = System.Net.NetworkInformation.NetworkInterface.GetAllNetworkInterfaces();

                foreach (var adapter in networkInterfaces)
                {
                    if (adapter.OperationalStatus == System.Net.NetworkInformation.OperationalStatus.Up &&
                        adapter.NetworkInterfaceType != System.Net.NetworkInformation.NetworkInterfaceType.Loopback)
                    {
                        info.AppendLine($"适配器名称：{adapter.Name}");
                        info.AppendLine($"描述：{adapter.Description}");
                        info.AppendLine($"类型：{adapter.NetworkInterfaceType}");
                        info.AppendLine($"状态：{adapter.OperationalStatus}");
                        info.AppendLine($"速度：{adapter.Speed / 1000000} Mbps");

                        var properties = adapter.GetIPProperties();
                        foreach (var unicast in properties.UnicastAddresses)
                        {
                            if (unicast.Address.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork)
                            {
                                info.AppendLine($"IP地址：{unicast.Address}");
                                info.AppendLine($"子网掩码：{unicast.IPv4Mask}");
                            }
                        }

                        if (properties.GatewayAddresses.Count > 0)
                        {
                            info.AppendLine($"默认网关：{properties.GatewayAddresses[0].Address}");
                        }

                        info.AppendLine("---");
                    }
                }

                return info.ToString();
            }
            catch (Exception ex)
            {
                return $"获取网络适配器信息失败：{ex.Message}";
            }
        }
    }

    /// <summary>
    /// 激活工具实现
    /// </summary>
    public class ActivationTool : ExecutableBaseTool
    {
        public ActivationTool(ILoggingService logger, IConfigurationService configService)
            : base(logger, configService)
        {
            Icon = "🔑";
        }

        public override string Name => "激活工具";

        public override string Description => "Windows系统激活工具";

        public override ToolCategory Category => ToolCategory.系统工具;

        protected override string GetExecutablePath()
        {
            return GetConfigValue("Tools.System.ActivationTool.Path", @"\\t-018254\临时文件中转\激活工具\KMS_VL_ALL_AIO_CN.cmd");
        }

        protected override bool RequireAdminRights()
        {
            return GetConfigValue("Tools.System.ActivationTool.RequireAdmin", true);
        }

        protected override async Task<ToolResult> ExecuteInternal(ToolParameters parameters)
        {
            // 显示确认对话框
            var result = MessageBox.Show(
                "您即将运行系统激活工具，此操作可能会修改系统设置。\n\n是否确认继续？",
                "确认运行激活工具",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Warning);

            if (result != DialogResult.Yes)
            {
                return ToolResult.CreateFailure("用户取消了操作");
            }

            return await base.ExecuteInternal(parameters);
        }

        public override ValidationResult ValidateConfiguration()
        {
            var baseValidation = base.ValidateConfiguration();
            if (!baseValidation.IsValid)
            {
                // 如果文件不存在，提供更友好的错误信息
                var path = GetExecutablePath();
                if (!System.IO.File.Exists(path))
                {
                    return ValidationResult.Invalid($"激活工具文件不存在：{path}\n\n请确认：\n1. 网络连接正常\n2. 有权限访问该网络路径\n3. 文件确实存在");
                }
            }

            return baseValidation;
        }


    }
}
