using System;

namespace 工具箱.Infrastructure.ErrorHandling
{
    /// <summary>
    /// 错误信息模型
    /// </summary>
    public class ErrorInfo
    {
        /// <summary>
        /// 原始异常对象
        /// </summary>
        public Exception Exception { get; set; }

        /// <summary>
        /// 用户友好的错误消息
        /// </summary>
        public string UserMessage { get; set; }

        /// <summary>
        /// 错误上下文
        /// </summary>
        public string Context { get; set; }

        /// <summary>
        /// 错误严重程度
        /// </summary>
        public ErrorSeverity Severity { get; set; } = ErrorSeverity.Error;

        /// <summary>
        /// 建议解决方案
        /// </summary>
        public string[] Suggestions { get; set; }

        /// <summary>
        /// 技术详情
        /// </summary>
        public string TechnicalDetails { get; set; }

        /// <summary>
        /// 错误发生时间
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// 是否为静默错误（不显示对话框）
        /// </summary>
        public bool IsSilent { get; set; } = false;

        /// <summary>
        /// 错误代码
        /// </summary>
        public string ErrorCode { get; set; }

        /// <summary>
        /// 是否可重试
        /// </summary>
        public bool IsRetryable { get; set; } = false;
    }

    /// <summary>
    /// 错误严重程度枚举
    /// </summary>
    public enum ErrorSeverity
    {
        /// <summary>
        /// 信息
        /// </summary>
        Info = 0,

        /// <summary>
        /// 警告
        /// </summary>
        Warning = 1,

        /// <summary>
        /// 错误
        /// </summary>
        Error = 2,

        /// <summary>
        /// 严重错误
        /// </summary>
        Critical = 3
    }

    /// <summary>
    /// 错误处理结果
    /// </summary>
    public class ErrorHandlingResult
    {
        /// <summary>
        /// 是否处理成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public ErrorInfo ErrorInfo { get; set; }

        /// <summary>
        /// 处理过程中的异常
        /// </summary>
        public Exception HandlingException { get; set; }

        /// <summary>
        /// 处理消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 创建成功结果
        /// </summary>
        /// <param name="message">消息</param>
        /// <returns>成功结果</returns>
        public static ErrorHandlingResult Success(string message = null)
        {
            return new ErrorHandlingResult
            {
                IsSuccess = true,
                Message = message
            };
        }

        /// <summary>
        /// 创建已处理结果
        /// </summary>
        /// <param name="errorInfo">错误信息</param>
        /// <returns>已处理结果</returns>
        public static ErrorHandlingResult Handled(ErrorInfo errorInfo)
        {
            return new ErrorHandlingResult
            {
                IsSuccess = true,
                ErrorInfo = errorInfo,
                Message = "异常已处理"
            };
        }

        /// <summary>
        /// 创建失败结果
        /// </summary>
        /// <param name="handlingException">处理异常</param>
        /// <returns>失败结果</returns>
        public static ErrorHandlingResult Failed(Exception handlingException)
        {
            return new ErrorHandlingResult
            {
                IsSuccess = false,
                HandlingException = handlingException,
                Message = "异常处理失败"
            };
        }
    }

    /// <summary>
    /// 自定义业务异常基类
    /// </summary>
    public abstract class BusinessException : Exception
    {
        /// <summary>
        /// 错误代码
        /// </summary>
        public string ErrorCode { get; }

        /// <summary>
        /// 用户友好的错误消息
        /// </summary>
        public string UserMessage { get; }

        /// <summary>
        /// 是否可重试
        /// </summary>
        public bool IsRetryable { get; }

        protected BusinessException(string errorCode, string userMessage, bool isRetryable = false)
            : base(userMessage)
        {
            ErrorCode = errorCode;
            UserMessage = userMessage;
            IsRetryable = isRetryable;
        }

        protected BusinessException(string errorCode, string userMessage, Exception innerException, bool isRetryable = false)
            : base(userMessage, innerException)
        {
            ErrorCode = errorCode;
            UserMessage = userMessage;
            IsRetryable = isRetryable;
        }
    }

    /// <summary>
    /// 工具执行异常
    /// </summary>
    public class ToolExecutionException : BusinessException
    {
        public string ToolName { get; }

        public ToolExecutionException(string toolName, string message, bool isRetryable = true)
            : base($"TOOL_EXECUTION_ERROR", $"工具 {toolName} 执行失败: {message}", isRetryable)
        {
            ToolName = toolName;
        }

        public ToolExecutionException(string toolName, string message, Exception innerException, bool isRetryable = true)
            : base($"TOOL_EXECUTION_ERROR", $"工具 {toolName} 执行失败: {message}", innerException, isRetryable)
        {
            ToolName = toolName;
        }
    }

    /// <summary>
    /// 配置异常
    /// </summary>
    public class ConfigurationException : BusinessException
    {
        public string ConfigKey { get; }

        public ConfigurationException(string configKey, string message)
            : base("CONFIGURATION_ERROR", $"配置错误 ({configKey}): {message}")
        {
            ConfigKey = configKey;
        }

        public ConfigurationException(string configKey, string message, Exception innerException)
            : base("CONFIGURATION_ERROR", $"配置错误 ({configKey}): {message}", innerException)
        {
            ConfigKey = configKey;
        }
    }

    /// <summary>
    /// 网络异常
    /// </summary>
    public class NetworkException : BusinessException
    {
        public string Url { get; }

        public NetworkException(string url, string message, bool isRetryable = true)
            : base("NETWORK_ERROR", $"网络错误: {message}", isRetryable)
        {
            Url = url;
        }

        public NetworkException(string url, string message, Exception innerException, bool isRetryable = true)
            : base("NETWORK_ERROR", $"网络错误: {message}", innerException, isRetryable)
        {
            Url = url;
        }
    }

    /// <summary>
    /// 文件系统异常
    /// </summary>
    public class FileSystemException : BusinessException
    {
        public string FilePath { get; }

        public FileSystemException(string filePath, string message, bool isRetryable = false)
            : base("FILESYSTEM_ERROR", $"文件系统错误: {message}", isRetryable)
        {
            FilePath = filePath;
        }

        public FileSystemException(string filePath, string message, Exception innerException, bool isRetryable = false)
            : base("FILESYSTEM_ERROR", $"文件系统错误: {message}", innerException, isRetryable)
        {
            FilePath = filePath;
        }
    }
}
