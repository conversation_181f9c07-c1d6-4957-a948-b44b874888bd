using System;
using System.Collections.Generic;
using 工具箱.Core.Interfaces;

namespace 工具箱.Core.Models
{
    /// <summary>
    /// 工具配置模型
    /// </summary>
    public class ToolConfiguration : IConfigurationSection
    {
        public string SectionName => "Tools";

        /// <summary>
        /// AI工具配置
        /// </summary>
        public AIToolsConfig AITools { get; set; } = new AIToolsConfig();

        /// <summary>
        /// 系统工具配置
        /// </summary>
        public SystemToolsConfig SystemTools { get; set; } = new SystemToolsConfig();

        /// <summary>
        /// 网络工具配置
        /// </summary>
        public NetworkToolsConfig NetworkTools { get; set; } = new NetworkToolsConfig();

        /// <summary>
        /// 实用工具配置
        /// </summary>
        public UtilityToolsConfig UtilityTools { get; set; } = new UtilityToolsConfig();

        public void LoadFrom(IConfigurationService configService)
        {
            AITools.LoadFrom(configService);
            SystemTools.LoadFrom(configService);
            NetworkTools.LoadFrom(configService);
            UtilityTools.LoadFrom(configService);
        }

        public void SaveTo(IConfigurationService configService)
        {
            AITools.SaveTo(configService);
            SystemTools.SaveTo(configService);
            NetworkTools.SaveTo(configService);
            UtilityTools.SaveTo(configService);
        }

        public ValidationResult Validate()
        {
            var errors = new List<string>();
            var warnings = new List<string>();

            var aiValidation = AITools.Validate();
            if (!aiValidation.IsValid)
                errors.AddRange(aiValidation.Errors);
            warnings.AddRange(aiValidation.Warnings);

            var systemValidation = SystemTools.Validate();
            if (!systemValidation.IsValid)
                errors.AddRange(systemValidation.Errors);
            warnings.AddRange(systemValidation.Warnings);

            var networkValidation = NetworkTools.Validate();
            if (!networkValidation.IsValid)
                errors.AddRange(networkValidation.Errors);
            warnings.AddRange(networkValidation.Warnings);

            var utilityValidation = UtilityTools.Validate();
            if (!utilityValidation.IsValid)
                errors.AddRange(utilityValidation.Errors);
            warnings.AddRange(utilityValidation.Warnings);

            return new ValidationResult
            {
                IsValid = errors.Count == 0,
                Errors = errors.ToArray(),
                Warnings = warnings.ToArray()
            };
        }
    }

    /// <summary>
    /// AI工具配置
    /// </summary>
    public class AIToolsConfig : IConfigurationSection
    {
        public string SectionName => "Tools.AI";

        public ToolItemConfig ChatGPT { get; set; } = new ToolItemConfig
        {
            Enabled = true,
            Url = "https://fastgpt.atlbattery.com/chat/share?shareId=652f6aaec6e21c41b67348d5"
        };

        public ToolItemConfig DeepSeek { get; set; } = new ToolItemConfig
        {
            Enabled = true,
            Url = "https://chat.deepseek.com/sign_in"
        };

        public ToolItemConfig PPTAssistant { get; set; } = new ToolItemConfig
        {
            Enabled = true,
            Url = "https://kimi.moonshot.cn/kimiplus/cvvm7bkheutnihqi2100"
        };

        public ToolItemConfig XiaoHuanXiong { get; set; } = new ToolItemConfig
        {
            Enabled = true,
            Url = "https://xiaohuanxiong.com/"
        };

        public ToolItemConfig DigitalHuman { get; set; } = new ToolItemConfig
        {
            Enabled = true,
            Url = "https://www.youyan3d.com/platform?from=drlx_yhaigc"
        };

        public ToolItemConfig TextToImage { get; set; } = new ToolItemConfig
        {
            Enabled = true,
            Url = "https://www.liblib.art/?sourceId=000147&qhclickid=e2dba2386860b107"
        };

        public ToolItemConfig TextToVideo { get; set; } = new ToolItemConfig
        {
            Enabled = true,
            Url = "https://app.klingai.com/cn/activity-zone?id=726928327164321859"
        };

        public ToolItemConfig TextToVoice { get; set; } = new ToolItemConfig
        {
            Enabled = true,
            Url = "https://noiz.ai/landing"
        };

        public ToolItemConfig TextToPPT { get; set; } = new ToolItemConfig
        {
            Enabled = false,
            Url = "待开发"
        };

        public void LoadFrom(IConfigurationService configService)
        {
            ChatGPT = LoadToolConfig(configService, "Tools.AI.ChatGPT", ChatGPT);
            DeepSeek = LoadToolConfig(configService, "Tools.AI.DeepSeek", DeepSeek);
            PPTAssistant = LoadToolConfig(configService, "Tools.AI.PPTAssistant", PPTAssistant);
            XiaoHuanXiong = LoadToolConfig(configService, "Tools.AI.XiaoHuanXiong", XiaoHuanXiong);
            DigitalHuman = LoadToolConfig(configService, "Tools.AI.DigitalHuman", DigitalHuman);
            TextToImage = LoadToolConfig(configService, "Tools.AI.TextToImage", TextToImage);
            TextToVideo = LoadToolConfig(configService, "Tools.AI.TextToVideo", TextToVideo);
            TextToVoice = LoadToolConfig(configService, "Tools.AI.TextToVoice", TextToVoice);
            TextToPPT = LoadToolConfig(configService, "Tools.AI.TextToPPT", TextToPPT);
        }

        public void SaveTo(IConfigurationService configService)
        {
            SaveToolConfig(configService, "Tools.AI.ChatGPT", ChatGPT);
            SaveToolConfig(configService, "Tools.AI.DeepSeek", DeepSeek);
            SaveToolConfig(configService, "Tools.AI.PPTAssistant", PPTAssistant);
            SaveToolConfig(configService, "Tools.AI.XiaoHuanXiong", XiaoHuanXiong);
            SaveToolConfig(configService, "Tools.AI.DigitalHuman", DigitalHuman);
            SaveToolConfig(configService, "Tools.AI.TextToImage", TextToImage);
            SaveToolConfig(configService, "Tools.AI.TextToVideo", TextToVideo);
            SaveToolConfig(configService, "Tools.AI.TextToVoice", TextToVoice);
            SaveToolConfig(configService, "Tools.AI.TextToPPT", TextToPPT);
        }

        public ValidationResult Validate()
        {
            var errors = new List<string>();
            var warnings = new List<string>();

            ValidateToolConfig("ChatGPT", ChatGPT, errors, warnings);
            ValidateToolConfig("DeepSeek", DeepSeek, errors, warnings);
            ValidateToolConfig("PPTAssistant", PPTAssistant, errors, warnings);
            ValidateToolConfig("XiaoHuanXiong", XiaoHuanXiong, errors, warnings);
            ValidateToolConfig("DigitalHuman", DigitalHuman, errors, warnings);
            ValidateToolConfig("TextToImage", TextToImage, errors, warnings);
            ValidateToolConfig("TextToVideo", TextToVideo, errors, warnings);
            ValidateToolConfig("TextToVoice", TextToVoice, errors, warnings);
            ValidateToolConfig("TextToPPT", TextToPPT, errors, warnings);

            return new ValidationResult
            {
                IsValid = errors.Count == 0,
                Errors = errors.ToArray(),
                Warnings = warnings.ToArray()
            };
        }

        private ToolItemConfig LoadToolConfig(IConfigurationService configService, string key, ToolItemConfig defaultConfig)
        {
            return new ToolItemConfig
            {
                Enabled = configService.GetValue($"{key}.Enabled", defaultConfig.Enabled),
                Url = configService.GetValue($"{key}.Url", defaultConfig.Url),
                Path = configService.GetValue($"{key}.Path", defaultConfig.Path),
                Arguments = configService.GetValue($"{key}.Arguments", defaultConfig.Arguments),
                RequireAdmin = configService.GetValue($"{key}.RequireAdmin", defaultConfig.RequireAdmin)
            };
        }

        private void SaveToolConfig(IConfigurationService configService, string key, ToolItemConfig config)
        {
            configService.SetValue($"{key}.Enabled", config.Enabled);
            configService.SetValue($"{key}.Url", config.Url);
            configService.SetValue($"{key}.Path", config.Path);
            configService.SetValue($"{key}.Arguments", config.Arguments);
            configService.SetValue($"{key}.RequireAdmin", config.RequireAdmin);
        }

        private void ValidateToolConfig(string toolName, ToolItemConfig config, List<string> errors, List<string> warnings)
        {
            if (config.Enabled)
            {
                if (string.IsNullOrWhiteSpace(config.Url) && string.IsNullOrWhiteSpace(config.Path))
                {
                    errors.Add($"AI工具 {toolName} 已启用但未配置URL或路径");
                }
                else if (!string.IsNullOrWhiteSpace(config.Url) && !Uri.IsWellFormedUriString(config.Url, UriKind.Absolute))
                {
                    warnings.Add($"AI工具 {toolName} 的URL格式可能不正确: {config.Url}");
                }
            }
        }
    }

    /// <summary>
    /// 系统工具配置
    /// </summary>
    public class SystemToolsConfig : IConfigurationSection
    {
        public string SectionName => "Tools.System";

        public ToolItemConfig ReleaseMemory { get; set; } = new ToolItemConfig { Enabled = true };
        public ToolItemConfig ChangePassword { get; set; } = new ToolItemConfig { Enabled = true };
        public ToolItemConfig IPInfo { get; set; } = new ToolItemConfig { Enabled = true };
        public ToolItemConfig ActivationTool { get; set; } = new ToolItemConfig
        {
            Enabled = true,
            Path = @"\\t-018254\临时文件中转\激活工具\KMS_VL_ALL_AIO_CN.cmd",
            RequireAdmin = true
        };

        public void LoadFrom(IConfigurationService configService)
        {
            ReleaseMemory = LoadToolConfig(configService, "Tools.System.ReleaseMemory", ReleaseMemory);
            ChangePassword = LoadToolConfig(configService, "Tools.System.ChangePassword", ChangePassword);
            IPInfo = LoadToolConfig(configService, "Tools.System.IPInfo", IPInfo);
            ActivationTool = LoadToolConfig(configService, "Tools.System.ActivationTool", ActivationTool);
        }

        public void SaveTo(IConfigurationService configService)
        {
            SaveToolConfig(configService, "Tools.System.ReleaseMemory", ReleaseMemory);
            SaveToolConfig(configService, "Tools.System.ChangePassword", ChangePassword);
            SaveToolConfig(configService, "Tools.System.IPInfo", IPInfo);
            SaveToolConfig(configService, "Tools.System.ActivationTool", ActivationTool);
        }

        public ValidationResult Validate()
        {
            var errors = new List<string>();
            var warnings = new List<string>();

            if (ActivationTool.Enabled && !string.IsNullOrWhiteSpace(ActivationTool.Path))
            {
                if (!System.IO.File.Exists(ActivationTool.Path))
                {
                    warnings.Add($"激活工具文件不存在: {ActivationTool.Path}");
                }
            }

            return new ValidationResult
            {
                IsValid = errors.Count == 0,
                Errors = errors.ToArray(),
                Warnings = warnings.ToArray()
            };
        }

        private ToolItemConfig LoadToolConfig(IConfigurationService configService, string key, ToolItemConfig defaultConfig)
        {
            return new ToolItemConfig
            {
                Enabled = configService.GetValue($"{key}.Enabled", defaultConfig.Enabled),
                Url = configService.GetValue($"{key}.Url", defaultConfig.Url),
                Path = configService.GetValue($"{key}.Path", defaultConfig.Path),
                Arguments = configService.GetValue($"{key}.Arguments", defaultConfig.Arguments),
                RequireAdmin = configService.GetValue($"{key}.RequireAdmin", defaultConfig.RequireAdmin)
            };
        }

        private void SaveToolConfig(IConfigurationService configService, string key, ToolItemConfig config)
        {
            configService.SetValue($"{key}.Enabled", config.Enabled);
            configService.SetValue($"{key}.Url", config.Url);
            configService.SetValue($"{key}.Path", config.Path);
            configService.SetValue($"{key}.Arguments", config.Arguments);
            configService.SetValue($"{key}.RequireAdmin", config.RequireAdmin);
        }
    }

    /// <summary>
    /// 网络工具配置
    /// </summary>
    public class NetworkToolsConfig : IConfigurationSection
    {
        public string SectionName => "Tools.Network";

        public ToolItemConfig NvtOa { get; set; } = new ToolItemConfig
        {
            Enabled = true,
            Url = "https://oa.nvtpower.com/login_single_random.jsp"
        };

        public ToolItemConfig Baidu { get; set; } = new ToolItemConfig
        {
            Enabled = true,
            Url = "https://www.baidu.com/"
        };

        public ToolItemConfig FileConvert { get; set; } = new ToolItemConfig
        {
            Enabled = true,
            Url = "https://tools.pdf24.org/zh/edit-pdf"
        };

        public void LoadFrom(IConfigurationService configService)
        {
            NvtOa = LoadToolConfig(configService, "Tools.Network.NvtOa", NvtOa);
            Baidu = LoadToolConfig(configService, "Tools.Network.Baidu", Baidu);
            FileConvert = LoadToolConfig(configService, "Tools.Network.FileConvert", FileConvert);
        }

        public void SaveTo(IConfigurationService configService)
        {
            SaveToolConfig(configService, "Tools.Network.NvtOa", NvtOa);
            SaveToolConfig(configService, "Tools.Network.Baidu", Baidu);
            SaveToolConfig(configService, "Tools.Network.FileConvert", FileConvert);
        }

        public ValidationResult Validate()
        {
            var errors = new List<string>();
            var warnings = new List<string>();

            ValidateUrlTool("NvtOa", NvtOa, errors, warnings);
            ValidateUrlTool("Baidu", Baidu, errors, warnings);
            ValidateUrlTool("FileConvert", FileConvert, errors, warnings);

            return new ValidationResult
            {
                IsValid = errors.Count == 0,
                Errors = errors.ToArray(),
                Warnings = warnings.ToArray()
            };
        }

        private ToolItemConfig LoadToolConfig(IConfigurationService configService, string key, ToolItemConfig defaultConfig)
        {
            return new ToolItemConfig
            {
                Enabled = configService.GetValue($"{key}.Enabled", defaultConfig.Enabled),
                Url = configService.GetValue($"{key}.Url", defaultConfig.Url),
                Path = configService.GetValue($"{key}.Path", defaultConfig.Path),
                Arguments = configService.GetValue($"{key}.Arguments", defaultConfig.Arguments),
                RequireAdmin = configService.GetValue($"{key}.RequireAdmin", defaultConfig.RequireAdmin)
            };
        }

        private void SaveToolConfig(IConfigurationService configService, string key, ToolItemConfig config)
        {
            configService.SetValue($"{key}.Enabled", config.Enabled);
            configService.SetValue($"{key}.Url", config.Url);
            configService.SetValue($"{key}.Path", config.Path);
            configService.SetValue($"{key}.Arguments", config.Arguments);
            configService.SetValue($"{key}.RequireAdmin", config.RequireAdmin);
        }

        private void ValidateUrlTool(string toolName, ToolItemConfig config, List<string> errors, List<string> warnings)
        {
            if (config.Enabled && !string.IsNullOrWhiteSpace(config.Url))
            {
                if (!Uri.IsWellFormedUriString(config.Url, UriKind.Absolute))
                {
                    warnings.Add($"网络工具 {toolName} 的URL格式可能不正确: {config.Url}");
                }
            }
        }
    }

    /// <summary>
    /// 实用工具配置
    /// </summary>
    public class UtilityToolsConfig : IConfigurationSection
    {
        public string SectionName => "Tools.Utility";

        public ToolItemConfig SearchFiles { get; set; } = new ToolItemConfig { Enabled = true };
        public ToolItemConfig MindMap { get; set; } = new ToolItemConfig
        {
            Enabled = true,
            Url = "https://mermaid.shizhuoran.top/"
        };

        public ToolItemConfig PrivacyLock { get; set; } = new ToolItemConfig
        {
            Enabled = true,
            Url = "http://172.18.21.14:37799/webroot/decision/login?origin=1a7fbec8-c32a-41b7-bbb4-acbe8dbf7e21"
        };

        public void LoadFrom(IConfigurationService configService)
        {
            SearchFiles = LoadToolConfig(configService, "Tools.Utility.SearchFiles", SearchFiles);
            MindMap = LoadToolConfig(configService, "Tools.Utility.MindMap", MindMap);
            PrivacyLock = LoadToolConfig(configService, "Tools.Utility.PrivacyLock", PrivacyLock);
        }

        public void SaveTo(IConfigurationService configService)
        {
            SaveToolConfig(configService, "Tools.Utility.SearchFiles", SearchFiles);
            SaveToolConfig(configService, "Tools.Utility.MindMap", MindMap);
            SaveToolConfig(configService, "Tools.Utility.PrivacyLock", PrivacyLock);
        }

        public ValidationResult Validate()
        {
            var errors = new List<string>();
            var warnings = new List<string>();

            ValidateUrlTool("MindMap", MindMap, errors, warnings);
            ValidateUrlTool("PrivacyLock", PrivacyLock, errors, warnings);

            return new ValidationResult
            {
                IsValid = errors.Count == 0,
                Errors = errors.ToArray(),
                Warnings = warnings.ToArray()
            };
        }

        private ToolItemConfig LoadToolConfig(IConfigurationService configService, string key, ToolItemConfig defaultConfig)
        {
            return new ToolItemConfig
            {
                Enabled = configService.GetValue($"{key}.Enabled", defaultConfig.Enabled),
                Url = configService.GetValue($"{key}.Url", defaultConfig.Url),
                Path = configService.GetValue($"{key}.Path", defaultConfig.Path),
                Arguments = configService.GetValue($"{key}.Arguments", defaultConfig.Arguments),
                RequireAdmin = configService.GetValue($"{key}.RequireAdmin", defaultConfig.RequireAdmin)
            };
        }

        private void SaveToolConfig(IConfigurationService configService, string key, ToolItemConfig config)
        {
            configService.SetValue($"{key}.Enabled", config.Enabled);
            configService.SetValue($"{key}.Url", config.Url);
            configService.SetValue($"{key}.Path", config.Path);
            configService.SetValue($"{key}.Arguments", config.Arguments);
            configService.SetValue($"{key}.RequireAdmin", config.RequireAdmin);
        }

        private void ValidateUrlTool(string toolName, ToolItemConfig config, List<string> errors, List<string> warnings)
        {
            if (config.Enabled && !string.IsNullOrWhiteSpace(config.Url))
            {
                if (!Uri.IsWellFormedUriString(config.Url, UriKind.Absolute))
                {
                    warnings.Add($"实用工具 {toolName} 的URL格式可能不正确: {config.Url}");
                }
            }
        }
    }

    /// <summary>
    /// 工具项配置
    /// </summary>
    public class ToolItemConfig
    {
        public bool Enabled { get; set; } = true;
        public string Url { get; set; }
        public string Path { get; set; }
        public string Arguments { get; set; }
        public bool RequireAdmin { get; set; } = false;
        public Dictionary<string, object> CustomProperties { get; set; } = new Dictionary<string, object>();
    }
}
