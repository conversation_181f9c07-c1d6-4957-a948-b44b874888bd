using System;
using System.IO;
using System.Threading.Tasks;
using System.Windows.Forms;
using AntdUI;

namespace 工具箱
{
    public partial class FileSearchForm : Form
    {
        public FileSearchForm()
        {
            InitializeComponent();
        }

        private async void btnSearch_Click(object sender, EventArgs e)
        {
            string searchTerm = txtSearchTerm.Text;
            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                MessageBox.Show("请输入搜索关键词。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            lvResults.Items.Clear();
            btnSearch.Enabled = false;
            lblStatus.Text = "正在搜索...";

            try
            {
                await Task.Run(() => SearchFiles(searchTerm));
            }
            catch (Exception ex)
            {
                MessageBox.Show("搜索过程中发生错误: " + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnSearch.Enabled = true;
                lblStatus.Text = "搜索完成。";
            }
        }

        private void SearchFiles(string searchTerm)
        {
            foreach (var drive in DriveInfo.GetDrives())
            {
                if (drive.IsReady)
                {
                    SearchInDirectory(drive.RootDirectory, searchTerm);
                }
            }
        }

        private void SearchInDirectory(DirectoryInfo di, string searchTerm)
        {
            try
            {
                foreach (var file in di.EnumerateFiles("*" + searchTerm + "*"))
                {
                    AddResult(file.FullName);
                }

                foreach (var subDir in di.EnumerateDirectories())
                {
                    SearchInDirectory(subDir, searchTerm);
                }
            }
            catch (UnauthorizedAccessException)
            {
                // 忽略没有权限访问的文件夹
            }
            catch (Exception)
            {
                // 忽略其他目录相关的错误
            }
        }

        private void AddResult(string filePath)
        {
            if (lvResults.InvokeRequired)
            {
                lvResults.Invoke(new Action<string>(AddResult), filePath);
            }
            else
            {
                lvResults.Items.Add(filePath);
            }
        }
    }
}