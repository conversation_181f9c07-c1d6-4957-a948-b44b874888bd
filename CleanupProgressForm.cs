using System;
using System.Windows.Forms;

namespace 工具箱
{
    public partial class CleanupProgressForm : Form
    {
        private readonly SystemCleaner _cleaner;

        public CleanupProgressForm()
        {
            InitializeComponent();
            _cleaner = new SystemCleaner();
            _cleaner.OnFileDeleted += OnFileDeleted;
            _cleaner.OnProgressChanged += OnProgressChanged;
            _cleaner.OnCleaningComplete += OnCleaningComplete;
        }

        protected override async void OnLoad(EventArgs e)
        {
            base.OnLoad(e);
            await _cleaner.CleanAsync();
        }

        private void OnFileDeleted(string filePath)
        {
            if (this.IsHandleCreated)
            {
                this.Invoke((MethodInvoker)delegate {
                    lbDeletedFiles.Items.Insert(0, $"已删除: {filePath}");
                    if (lbDeletedFiles.Items.Count > 200) // 保持列表不会无限增长
                    {
                        lbDeletedFiles.Items.RemoveAt(lbDeletedFiles.Items.Count - 1);
                    }
                });
            }
        }

        private void OnProgressChanged(int current, int total)
        {
            if (this.IsHandleCreated)
            {
                this.Invoke((MethodInvoker)delegate {
                    progressBar.Maximum = total;
                    progressBar.Value = current;
                    lblProgress.Text = $"进度: {current} / {total}";
                });
            }
        }

        private void OnCleaningComplete(long totalBytesCleaned)
        {
            if (this.IsHandleCreated)
            {
                this.Invoke((MethodInvoker)delegate {
                    MessageBox.Show($"清理完成！共释放 {(totalBytesCleaned / (1024.0 * 1024.0)):F2} MB 空间。", "完成", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    this.Close();
                });
            }
        }
    }
}