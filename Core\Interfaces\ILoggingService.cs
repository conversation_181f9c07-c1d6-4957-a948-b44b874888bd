using System;

namespace 工具箱.Core.Interfaces
{
    /// <summary>
    /// 日志服务接口，提供统一的日志记录功能
    /// </summary>
    public interface ILoggingService
    {
        /// <summary>
        /// 记录信息日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="args">格式化参数</param>
        void LogInfo(string message, params object[] args);

        /// <summary>
        /// 记录警告日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="args">格式化参数</param>
        void LogWarning(string message, params object[] args);

        /// <summary>
        /// 记录错误日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="args">格式化参数</param>
        void LogError(string message, params object[] args);

        /// <summary>
        /// 记录异常日志
        /// </summary>
        /// <param name="exception">异常对象</param>
        /// <param name="message">附加消息</param>
        void LogError(Exception exception, string message = null);

        /// <summary>
        /// 记录调试日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="args">格式化参数</param>
        void LogDebug(string message, params object[] args);

        /// <summary>
        /// 记录工具执行日志
        /// </summary>
        /// <param name="toolName">工具名称</param>
        /// <param name="duration">执行时长</param>
        /// <param name="success">是否成功</param>
        /// <param name="message">附加消息</param>
        void LogToolExecution(string toolName, TimeSpan duration, bool success, string message = null);

        /// <summary>
        /// 记录用户操作日志
        /// </summary>
        /// <param name="action">操作名称</param>
        /// <param name="details">操作详情</param>
        void LogUserAction(string action, string details = null);

        /// <summary>
        /// 清空日志
        /// </summary>
        void ClearLogs();

        /// <summary>
        /// 获取日志文件路径
        /// </summary>
        /// <returns>日志文件路径</returns>
        string GetLogFilePath();

        /// <summary>
        /// 设置日志级别
        /// </summary>
        /// <param name="level">日志级别</param>
        void SetLogLevel(LogLevel level);

        /// <summary>
        /// 获取当前日志级别
        /// </summary>
        /// <returns>当前日志级别</returns>
        LogLevel GetLogLevel();
    }

    /// <summary>
    /// 日志级别枚举
    /// </summary>
    public enum LogLevel
    {
        Debug = 0,
        Info = 1,
        Warning = 2,
        Error = 3,
        None = 4
    }

    /// <summary>
    /// 日志条目
    /// </summary>
    public class LogEntry
    {
        public DateTime Timestamp { get; set; } = DateTime.Now;
        public LogLevel Level { get; set; }
        public string Message { get; set; }
        public string Category { get; set; }
        public Exception Exception { get; set; }
        public string ThreadId { get; set; }
        public string Source { get; set; }

        public override string ToString()
        {
            var levelStr = Level.ToString().ToUpper().PadRight(7);
            var timestamp = Timestamp.ToString("yyyy-MM-dd HH:mm:ss.fff");
            var message = Exception != null ? $"{Message} | {Exception}" : Message;
            
            return $"[{timestamp}] [{levelStr}] {Category}: {message}";
        }
    }
}
