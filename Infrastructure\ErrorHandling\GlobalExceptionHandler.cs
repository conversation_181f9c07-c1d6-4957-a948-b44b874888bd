using System;
using System.IO;
using System.Net.NetworkInformation;
using System.Security;
using System.Threading.Tasks;
using System.Windows.Forms;
using 工具箱.Core.Interfaces;

namespace 工具箱.Infrastructure.ErrorHandling
{
    /// <summary>
    /// 全局异常处理器
    /// </summary>
    public class GlobalExceptionHandler
    {
        private readonly ILoggingService _logger;
        private readonly IConfigurationService _configService;

        public GlobalExceptionHandler(ILoggingService logger, IConfigurationService configService)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _configService = configService ?? throw new ArgumentNullException(nameof(configService));
        }

        /// <summary>
        /// 处理异常并显示用户友好的错误消息
        /// </summary>
        /// <param name="ex">异常对象</param>
        /// <param name="context">异常上下文</param>
        /// <param name="showDialog">是否显示错误对话框</param>
        /// <returns>处理结果</returns>
        public ErrorHandlingResult HandleException(Exception ex, string context = null, bool showDialog = true)
        {
            if (ex == null)
                return ErrorHandlingResult.Success();

            try
            {
                // 记录异常日志
                _logger.LogError(ex, context);

                // 分析异常类型并生成用户友好的消息
                var errorInfo = AnalyzeException(ex, context);

                // 显示错误对话框（如果需要）
                if (showDialog && !errorInfo.IsSilent)
                {
                    ShowErrorDialog(errorInfo);
                }

                return ErrorHandlingResult.Handled(errorInfo);
            }
            catch (Exception handlingEx)
            {
                // 异常处理本身发生异常，记录并返回失败结果
                try
                {
                    _logger.LogError(handlingEx, "异常处理器本身发生异常");
                }
                catch
                {
                    // 忽略日志记录失败
                }

                return ErrorHandlingResult.Failed(handlingEx);
            }
        }

        /// <summary>
        /// 异步处理异常
        /// </summary>
        /// <param name="ex">异常对象</param>
        /// <param name="context">异常上下文</param>
        /// <param name="showDialog">是否显示错误对话框</param>
        /// <returns>处理结果</returns>
        public async Task<ErrorHandlingResult> HandleExceptionAsync(Exception ex, string context = null, bool showDialog = true)
        {
            return await Task.Run(() => HandleException(ex, context, showDialog));
        }

        /// <summary>
        /// 分析异常并生成错误信息
        /// </summary>
        /// <param name="ex">异常对象</param>
        /// <param name="context">异常上下文</param>
        /// <returns>错误信息</returns>
        private ErrorInfo AnalyzeException(Exception ex, string context)
        {
            var errorInfo = new ErrorInfo
            {
                Exception = ex,
                Context = context,
                Timestamp = DateTime.Now
            };

            // 根据异常类型生成用户友好的消息
            if (ex is UnauthorizedAccessException)
            {
                errorInfo.UserMessage = "权限不足，请以管理员身份运行程序";
                errorInfo.Severity = ErrorSeverity.Warning;
                errorInfo.Suggestions = new[]
                {
                    "右键点击程序图标，选择\"以管理员身份运行\"",
                    "检查文件或文件夹的访问权限",
                    "确保当前用户有足够的权限执行此操作"
                };
            }

            else if (ex is FileNotFoundException fileNotFound)
            {
                errorInfo.UserMessage = $"找不到指定的文件：{Path.GetFileName(fileNotFound.FileName)}";
                errorInfo.Severity = ErrorSeverity.Error;
                errorInfo.Suggestions = new[]
                {
                    "检查文件路径是否正确",
                    "确认文件是否存在",
                    "检查网络连接（如果是网络路径）"
                };
            }
            else if (ex is DirectoryNotFoundException)
            {
                errorInfo.UserMessage = "找不到指定的目录或路径";
                errorInfo.Severity = ErrorSeverity.Error;
                errorInfo.Suggestions = new[]
                {
                    "检查路径是否正确",
                    "确认目录是否存在",
                    "检查网络连接（如果是网络路径）"
                };
            }

            else if (ex is NetworkInformationException || ex is System.Net.WebException)
            {
                errorInfo.UserMessage = "网络连接失败，请检查网络设置";
                errorInfo.Severity = ErrorSeverity.Warning;
                errorInfo.Suggestions = new[]
                {
                    "检查网络连接是否正常",
                    "确认防火墙设置",
                    "检查代理服务器设置",
                    "稍后重试"
                };
            }
            else if (ex is SecurityException)
            {
                errorInfo.UserMessage = "安全策略阻止了此操作";
                errorInfo.Severity = ErrorSeverity.Warning;
                errorInfo.Suggestions = new[]
                {
                    "联系系统管理员",
                    "检查安全策略设置",
                    "以管理员身份运行程序"
                };
            }
            else if (ex is System.ComponentModel.Win32Exception win32Ex && win32Ex.NativeErrorCode == 1223)
            {
                errorInfo.UserMessage = "用户取消了权限提升请求";
                errorInfo.Severity = ErrorSeverity.Info;
                errorInfo.IsSilent = true; // 用户主动取消，不显示错误对话框
            }

            else if (ex is TimeoutException)
            {
                errorInfo.UserMessage = "操作超时，请稍后重试";
                errorInfo.Severity = ErrorSeverity.Warning;
                errorInfo.Suggestions = new[]
                {
                    "检查网络连接速度",
                    "稍后重试",
                    "联系技术支持"
                };
            }
            else if (ex is OutOfMemoryException)
            {
                errorInfo.UserMessage = "内存不足，请关闭一些程序后重试";
                errorInfo.Severity = ErrorSeverity.Error;
                errorInfo.Suggestions = new[]
                {
                    "关闭不必要的程序",
                    "重启应用程序",
                    "重启计算机"
                };
            }
            else if (ex is ArgumentException argEx)
            {
                errorInfo.UserMessage = "参数错误：" + argEx.Message;
                errorInfo.Severity = ErrorSeverity.Error;
                errorInfo.Suggestions = new[]
                {
                    "检查输入参数是否正确",
                    "联系技术支持"
                };
            }
            else if (ex is InvalidOperationException)
            {
                errorInfo.UserMessage = "当前状态下无法执行此操作";
                errorInfo.Severity = ErrorSeverity.Warning;
                errorInfo.Suggestions = new[]
                {
                    "检查操作的前置条件",
                    "稍后重试",
                    "重启应用程序"
                };
            }
            else
            {
                errorInfo.UserMessage = "操作失败，请稍后重试";
                errorInfo.Severity = ErrorSeverity.Error;
                errorInfo.Suggestions = new[]
                {
                    "稍后重试",
                    "重启应用程序",
                    "联系技术支持"
                };
            }

            // 添加技术详情（仅在调试模式或启用详细错误信息时）
            var showTechnicalDetails = _configService.GetValue("Application.ErrorHandling.ShowTechnicalDetails", false);
            if (showTechnicalDetails)
            {
                errorInfo.TechnicalDetails = ex.ToString();
            }

            return errorInfo;
        }

        /// <summary>
        /// 显示错误对话框
        /// </summary>
        /// <param name="errorInfo">错误信息</param>
        private void ShowErrorDialog(ErrorInfo errorInfo)
        {
            try
            {
                MessageBoxIcon icon;
                string title;

                switch (errorInfo.Severity)
                {
                    case ErrorSeverity.Info:
                        icon = MessageBoxIcon.Information;
                        title = "信息";
                        break;
                    case ErrorSeverity.Warning:
                        icon = MessageBoxIcon.Warning;
                        title = "警告";
                        break;
                    case ErrorSeverity.Error:
                        icon = MessageBoxIcon.Error;
                        title = "错误";
                        break;
                    default:
                        icon = MessageBoxIcon.Error;
                        title = "错误";
                        break;
                }

                var message = errorInfo.UserMessage;

                // 添加建议（如果有）
                if (errorInfo.Suggestions?.Length > 0)
                {
                    message += "\n\n建议解决方案：\n";
                    for (int i = 0; i < errorInfo.Suggestions.Length; i++)
                    {
                        message += $"{i + 1}. {errorInfo.Suggestions[i]}\n";
                    }
                }

                // 添加技术详情（如果启用）
                if (!string.IsNullOrEmpty(errorInfo.TechnicalDetails))
                {
                    message += "\n\n技术详情：\n" + errorInfo.TechnicalDetails;
                }

                MessageBox.Show(message, title, MessageBoxButtons.OK, icon);
            }
            catch (Exception ex)
            {
                // 显示错误对话框失败，尝试显示简单的错误消息
                try
                {
                    MessageBox.Show(
                        $"发生错误：{errorInfo.Exception.Message}",
                        "错误",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Error);
                }
                catch
                {
                    // 完全失败，记录到日志
                    _logger.LogError(ex, "显示错误对话框失败");
                }
            }
        }

        /// <summary>
        /// 设置全局异常处理
        /// </summary>
        public void SetupGlobalExceptionHandling()
        {
            // 设置应用程序域未处理异常处理
            AppDomain.CurrentDomain.UnhandledException += (sender, e) =>
            {
                if (e.ExceptionObject is Exception ex)
                {
                    HandleException(ex, "AppDomain.UnhandledException", true);
                }
            };

            // 设置Windows Forms未处理异常处理
            Application.ThreadException += (sender, e) =>
            {
                HandleException(e.Exception, "Application.ThreadException", true);
            };

            // 设置任务调度器未处理异常处理
            TaskScheduler.UnobservedTaskException += (sender, e) =>
            {
                HandleException(e.Exception, "TaskScheduler.UnobservedTaskException", false);
                e.SetObserved(); // 标记异常已被观察，防止程序崩溃
            };
        }
    }
}
