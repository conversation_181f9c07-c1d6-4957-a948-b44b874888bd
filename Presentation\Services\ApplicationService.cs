using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using 工具箱.Core.Interfaces;
using 工具箱.Core.Models;
using 工具箱.Infrastructure.DependencyInjection;
using 工具箱.Infrastructure.ErrorHandling;
using 工具箱.Infrastructure.Services;

namespace 工具箱.Presentation.Services
{
    /// <summary>
    /// 应用程序服务，管理整个应用的生命周期和核心功能
    /// </summary>
    public class ApplicationService
    {
        private readonly ServiceContainer _serviceContainer;
        private readonly ILoggingService _logger;
        private readonly IConfigurationService _configService;
        private readonly IToolManager _toolManager;
        private readonly GlobalExceptionHandler _exceptionHandler;
        private readonly ApplicationConfiguration _appConfig;

        public ApplicationService()
        {
            // 初始化服务容器
            _serviceContainer = new ServiceContainer()
                .ConfigureToolboxServices()
                .InitializeToolManager();

            // 解析核心服务
            _logger = _serviceContainer.Resolve<ILoggingService>();
            _configService = _serviceContainer.Resolve<IConfigurationService>();
            _toolManager = _serviceContainer.Resolve<IToolManager>();
            _exceptionHandler = _serviceContainer.Resolve<GlobalExceptionHandler>();

            // 加载应用配置
            _appConfig = new ApplicationConfiguration();
            _appConfig.LoadFrom(_configService);

            // 设置全局异常处理
            _exceptionHandler.SetupGlobalExceptionHandling();

            _logger.LogInfo("应用程序服务初始化完成");
        }

        /// <summary>
        /// 获取服务容器
        /// </summary>
        public ServiceContainer ServiceContainer => _serviceContainer;

        /// <summary>
        /// 获取日志服务
        /// </summary>
        public ILoggingService Logger => _logger;

        /// <summary>
        /// 获取配置服务
        /// </summary>
        public IConfigurationService ConfigService => _configService;

        /// <summary>
        /// 获取工具管理器
        /// </summary>
        public IToolManager ToolManager => _toolManager;

        /// <summary>
        /// 获取异常处理器
        /// </summary>
        public GlobalExceptionHandler ExceptionHandler => _exceptionHandler;

        /// <summary>
        /// 获取应用配置
        /// </summary>
        public ApplicationConfiguration AppConfig => _appConfig;

        /// <summary>
        /// 执行工具
        /// </summary>
        /// <param name="toolName">工具名称</param>
        /// <param name="parameters">执行参数</param>
        /// <returns>执行结果</returns>
        public async Task<ToolResult> ExecuteToolAsync(string toolName, ToolParameters parameters = null)
        {
            try
            {
                _logger.LogUserAction("ExecuteTool", toolName);
                return await _toolManager.ExecuteToolAsync(toolName, parameters);
            }
            catch (Exception ex)
            {
                _exceptionHandler.HandleException(ex, $"执行工具 {toolName}");
                return ToolResult.CreateFailure($"执行工具失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 搜索工具
        /// </summary>
        /// <param name="searchTerm">搜索关键词</param>
        /// <returns>匹配的工具集合</returns>
        public IEnumerable<ITool> SearchTools(string searchTerm)
        {
            try
            {
                return _toolManager.SearchTools(searchTerm);
            }
            catch (Exception ex)
            {
                _exceptionHandler.HandleException(ex, "搜索工具", false);
                return Enumerable.Empty<ITool>();
            }
        }

        /// <summary>
        /// 获取指定分类的工具
        /// </summary>
        /// <param name="category">工具分类</param>
        /// <returns>工具集合</returns>
        public IEnumerable<ITool> GetToolsByCategory(ToolCategory category)
        {
            try
            {
                return _toolManager.GetToolsByCategory(category);
            }
            catch (Exception ex)
            {
                _exceptionHandler.HandleException(ex, $"获取分类工具 {category}", false);
                return Enumerable.Empty<ITool>();
            }
        }

        /// <summary>
        /// 获取所有工具
        /// </summary>
        /// <returns>所有工具集合</returns>
        public IEnumerable<ITool> GetAllTools()
        {
            try
            {
                return _toolManager.GetAllTools();
            }
            catch (Exception ex)
            {
                _exceptionHandler.HandleException(ex, "获取所有工具", false);
                return Enumerable.Empty<ITool>();
            }
        }

        /// <summary>
        /// 根据名称获取工具
        /// </summary>
        /// <param name="toolName">工具名称</param>
        /// <returns>工具实例</returns>
        public ITool GetToolByName(string toolName)
        {
            try
            {
                return _toolManager.GetToolByName(toolName);
            }
            catch (Exception ex)
            {
                _exceptionHandler.HandleException(ex, $"获取工具 {toolName}", false);
                return null;
            }
        }

        /// <summary>
        /// 验证所有工具配置
        /// </summary>
        /// <returns>验证结果</returns>
        public Dictionary<string, ValidationResult> ValidateAllTools()
        {
            try
            {
                return _toolManager.ValidateAllTools();
            }
            catch (Exception ex)
            {
                _exceptionHandler.HandleException(ex, "验证工具配置", false);
                return new Dictionary<string, ValidationResult>();
            }
        }

        /// <summary>
        /// 保存配置
        /// </summary>
        public void SaveConfiguration()
        {
            try
            {
                _appConfig.SaveTo(_configService);
                _configService.Save();
                _logger.LogInfo("配置已保存");
            }
            catch (Exception ex)
            {
                _exceptionHandler.HandleException(ex, "保存配置");
            }
        }

        /// <summary>
        /// 重新加载配置
        /// </summary>
        public void ReloadConfiguration()
        {
            try
            {
                _configService.Reload();
                _appConfig.LoadFrom(_configService);
                _logger.LogInfo("配置已重新加载");
            }
            catch (Exception ex)
            {
                _exceptionHandler.HandleException(ex, "重新加载配置");
            }
        }

        /// <summary>
        /// 获取工具统计信息
        /// </summary>
        /// <returns>工具统计信息</returns>
        public ToolStatistics GetToolStatistics()
        {
            try
            {
                var toolManager = _toolManager as ToolManager;
                return toolManager?.GetStatistics() ?? new ToolStatistics();
            }
            catch (Exception ex)
            {
                _exceptionHandler.HandleException(ex, "获取工具统计信息", false);
                return new ToolStatistics();
            }
        }

        /// <summary>
        /// 处理异常
        /// </summary>
        /// <param name="ex">异常对象</param>
        /// <param name="context">异常上下文</param>
        /// <param name="showDialog">是否显示错误对话框</param>
        /// <returns>处理结果</returns>
        public ErrorHandlingResult HandleException(Exception ex, string context = null, bool showDialog = true)
        {
            return _exceptionHandler.HandleException(ex, context, showDialog);
        }

        /// <summary>
        /// 异步处理异常
        /// </summary>
        /// <param name="ex">异常对象</param>
        /// <param name="context">异常上下文</param>
        /// <param name="showDialog">是否显示错误对话框</param>
        /// <returns>处理结果</returns>
        public async Task<ErrorHandlingResult> HandleExceptionAsync(Exception ex, string context = null, bool showDialog = true)
        {
            return await _exceptionHandler.HandleExceptionAsync(ex, context, showDialog);
        }

        /// <summary>
        /// 应用程序关闭时的清理工作
        /// </summary>
        public void Shutdown()
        {
            try
            {
                _logger.LogInfo("应用程序正在关闭");

                // 保存配置
                SaveConfiguration();

                // 清理资源
                if (_logger is IDisposable disposableLogger)
                {
                    disposableLogger.Dispose();
                }

                _logger.LogInfo("应用程序关闭完成");
            }
            catch (Exception ex)
            {
                // 关闭时的异常不显示对话框
                _exceptionHandler.HandleException(ex, "应用程序关闭", false);
            }
        }

        /// <summary>
        /// 获取通知信息
        /// </summary>
        /// <returns>通知文本</returns>
        public async Task<string> GetNotificationTextAsync()
        {
            try
            {
                var notificationPath = _appConfig.Notification.NotificationFilePath;
                
                if (!_appConfig.Notification.EnableNotifications)
                {
                    return "通知功能已禁用";
                }

                if (string.IsNullOrWhiteSpace(notificationPath))
                {
                    return "通知文件路径未配置";
                }

                return await Task.Run(() =>
                {
                    try
                    {
                        if (System.IO.File.Exists(notificationPath))
                        {
                            var content = System.IO.File.ReadAllText(notificationPath, System.Text.Encoding.UTF8);
                            return string.IsNullOrWhiteSpace(content) ? "通知文件为空" : content.Trim();
                        }
                        else
                        {
                            return "无法访问通知文件，请检查网络连接";
                        }
                    }
                    catch (UnauthorizedAccessException)
                    {
                        return "没有权限访问通知文件";
                    }
                    catch (System.IO.DirectoryNotFoundException)
                    {
                        return "通知文件路径不存在";
                    }
                    catch (System.IO.FileNotFoundException)
                    {
                        return "通知文件未找到";
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "加载通知文件失败");
                        return $"加载通知失败: {ex.Message}";
                    }
                });
            }
            catch (Exception ex)
            {
                _exceptionHandler.HandleException(ex, "获取通知信息", false);
                return "获取通知信息失败";
            }
        }
    }
}
