using 工具箱.Core.Interfaces;
using 工具箱.Core.Models;

namespace 工具箱.Business.Tools.Network
{
    /// <summary>
    /// NVT-OA工具实现
    /// </summary>
    public class NvtOaTool : UrlBaseTool
    {
        public NvtOaTool(ILoggingService logger, IConfigurationService configService)
            : base(logger, configService)
        {
            Icon = "🏢";
        }

        public override string Name => "NVT-OA";

        public override string Description => "NVT办公自动化系统";

        public override ToolCategory Category => ToolCategory.网络工具;

        protected override string GetUrl()
        {
            return GetConfigValue("Tools.Network.NvtOa.Url", "https://oa.nvtpower.com/login_single_random.jsp");
        }

        public override ValidationResult ValidateConfiguration()
        {
            var baseValidation = base.ValidateConfiguration();
            if (!baseValidation.IsValid)
                return baseValidation;

            var url = GetUrl();
            if (url.Contains("oa.nvtpower.com"))
            {
                return ValidationResult.Valid();
            }

            return ValidationResult.Invalid("NVT-OA URL配置不正确");
        }
    }

    /// <summary>
    /// 百度工具实现
    /// </summary>
    public class BaiduTool : UrlBaseTool
    {
        public BaiduTool(ILoggingService logger, IConfigurationService configService)
            : base(logger, configService)
        {
            Icon = "🔍";
        }

        public override string Name => "百度";

        public override string Description => "百度搜索引擎";

        public override ToolCategory Category => ToolCategory.网络工具;

        protected override string GetUrl()
        {
            return GetConfigValue("Tools.Network.Baidu.Url", "https://www.baidu.com/");
        }
    }

    /// <summary>
    /// 文件转换工具实现
    /// </summary>
    public class FileConvertTool : UrlBaseTool
    {
        public FileConvertTool(ILoggingService logger, IConfigurationService configService)
            : base(logger, configService)
        {
            Icon = "🔄";
        }

        public override string Name => "文件转换";

        public override string Description => "在线PDF编辑和文件转换工具";

        public override ToolCategory Category => ToolCategory.网络工具;

        protected override string GetUrl()
        {
            return GetConfigValue("Tools.Network.FileConvert.Url", "https://tools.pdf24.org/zh/edit-pdf");
        }

        public override ValidationResult ValidateConfiguration()
        {
            var baseValidation = base.ValidateConfiguration();
            if (!baseValidation.IsValid)
                return baseValidation;

            var url = GetUrl();
            if (url.Contains("pdf24.org"))
            {
                return ValidationResult.Valid();
            }

            return ValidationResult.Invalid("文件转换工具URL配置不正确");
        }
    }
}
