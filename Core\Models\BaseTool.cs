using System;
using System.Diagnostics;
using System.Threading.Tasks;
using 工具箱.Core.Interfaces;

namespace 工具箱.Core.Models
{
    /// <summary>
    /// 工具基类，提供通用的工具实现
    /// </summary>
    public abstract class BaseTool : ITool
    {
        protected readonly ILoggingService _logger;
        protected readonly IConfigurationService _configService;

        protected BaseTool(ILoggingService logger, IConfigurationService configService)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _configService = configService ?? throw new ArgumentNullException(nameof(configService));
        }

        public abstract string Name { get; }
        public abstract string Description { get; }
        public abstract ToolCategory Category { get; }
        public virtual string Icon { get; protected set; }
        public virtual bool IsEnabled { get; protected set; } = true;

        public virtual bool CanExecute(ToolParameters parameters = null)
        {
            if (!IsEnabled)
            {
                _logger.LogWarning($"工具 {Name} 已被禁用");
                return false;
            }

            var validation = ValidateConfiguration();
            if (!validation.IsValid)
            {
                _logger.LogWarning($"工具 {Name} 配置验证失败: {string.Join(", ", validation.Errors)}");
                return false;
            }

            return CanExecuteInternal(parameters);
        }

        public async Task<ToolResult> ExecuteAsync(ToolParameters parameters = null)
        {
            var stopwatch = Stopwatch.StartNew();
            var toolName = Name;

            try
            {
                _logger.LogInfo($"开始执行工具: {toolName}");

                if (!CanExecute(parameters))
                {
                    var result = ToolResult.CreateFailure($"工具 {toolName} 无法执行");
                    result.ExecutionTime = stopwatch.Elapsed;
                    return result;
                }

                var executeResult = await ExecuteInternal(parameters);
                executeResult.ExecutionTime = stopwatch.Elapsed;

                _logger.LogToolExecution(toolName, stopwatch.Elapsed, executeResult.Success, executeResult.Message);

                if (executeResult.Success)
                {
                    _logger.LogInfo($"工具 {toolName} 执行成功: {executeResult.Message}");
                }
                else
                {
                    _logger.LogError($"工具 {toolName} 执行失败: {executeResult.Message}");
                }

                return executeResult;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                var result = ToolResult.CreateFailure($"工具 {toolName} 执行时发生异常: {ex.Message}", ex);
                result.ExecutionTime = stopwatch.Elapsed;

                _logger.LogError(ex, $"工具 {toolName} 执行异常");
                _logger.LogToolExecution(toolName, stopwatch.Elapsed, false, ex.Message);

                return result;
            }
        }

        public virtual ValidationResult ValidateConfiguration()
        {
            return ValidationResult.Valid();
        }

        /// <summary>
        /// 子类实现具体的执行逻辑
        /// </summary>
        /// <param name="parameters">执行参数</param>
        /// <returns>执行结果</returns>
        protected abstract Task<ToolResult> ExecuteInternal(ToolParameters parameters);

        /// <summary>
        /// 子类实现具体的执行条件检查
        /// </summary>
        /// <param name="parameters">执行参数</param>
        /// <returns>是否可以执行</returns>
        protected virtual bool CanExecuteInternal(ToolParameters parameters)
        {
            return true;
        }

        /// <summary>
        /// 获取配置值的辅助方法
        /// </summary>
        /// <typeparam name="T">配置值类型</typeparam>
        /// <param name="key">配置键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>配置值</returns>
        protected T GetConfigValue<T>(string key, T defaultValue = default(T))
        {
            return _configService.GetValue(key, defaultValue);
        }

        /// <summary>
        /// 设置配置值的辅助方法
        /// </summary>
        /// <typeparam name="T">配置值类型</typeparam>
        /// <param name="key">配置键</param>
        /// <param name="value">配置值</param>
        protected void SetConfigValue<T>(string key, T value)
        {
            _configService.SetValue(key, value);
        }

        /// <summary>
        /// 记录用户操作的辅助方法
        /// </summary>
        /// <param name="action">操作名称</param>
        /// <param name="details">操作详情</param>
        protected void LogUserAction(string action, string details = null)
        {
            _logger.LogUserAction($"{Name}.{action}", details);
        }
    }

    /// <summary>
    /// URL工具基类，用于打开网页的工具
    /// </summary>
    public abstract class UrlBaseTool : BaseTool
    {
        protected UrlBaseTool(ILoggingService logger, IConfigurationService configService)
            : base(logger, configService)
        {
        }

        /// <summary>
        /// 获取工具的URL
        /// </summary>
        protected abstract string GetUrl();

        protected override async Task<ToolResult> ExecuteInternal(ToolParameters parameters)
        {
            var url = GetUrl();
            
            if (string.IsNullOrWhiteSpace(url))
            {
                return ToolResult.CreateFailure("URL配置为空");
            }

            if (url == "待开发")
            {
                return ToolResult.CreateFailure("该功能正在开发中");
            }

            try
            {
                LogUserAction("OpenUrl", url);
                
                await Task.Run(() =>
                {
                    Process.Start(url);
                });

                return ToolResult.CreateSuccess($"已打开 {Name}");
            }
            catch (Exception ex)
            {
                return ToolResult.CreateFailure($"无法打开链接: {ex.Message}", ex);
            }
        }

        protected override bool CanExecuteInternal(ToolParameters parameters)
        {
            var url = GetUrl();
            return !string.IsNullOrWhiteSpace(url) && url != "待开发";
        }

        public override ValidationResult ValidateConfiguration()
        {
            var url = GetUrl();
            
            if (string.IsNullOrWhiteSpace(url))
            {
                return ValidationResult.Invalid($"工具 {Name} 的URL配置为空");
            }

            if (url == "待开发")
            {
                return ValidationResult.Invalid($"工具 {Name} 正在开发中");
            }

            if (!Uri.IsWellFormedUriString(url, UriKind.Absolute))
            {
                return ValidationResult.Invalid($"工具 {Name} 的URL格式不正确: {url}");
            }

            return ValidationResult.Valid();
        }
    }

    /// <summary>
    /// 可执行文件工具基类，用于启动外部程序的工具
    /// </summary>
    public abstract class ExecutableBaseTool : BaseTool
    {
        protected ExecutableBaseTool(ILoggingService logger, IConfigurationService configService)
            : base(logger, configService)
        {
        }

        /// <summary>
        /// 获取可执行文件路径
        /// </summary>
        protected abstract string GetExecutablePath();

        /// <summary>
        /// 获取启动参数
        /// </summary>
        protected virtual string GetArguments()
        {
            return string.Empty;
        }

        /// <summary>
        /// 是否需要管理员权限
        /// </summary>
        protected virtual bool RequireAdminRights()
        {
            return false;
        }

        protected override async Task<ToolResult> ExecuteInternal(ToolParameters parameters)
        {
            var executablePath = GetExecutablePath();
            var arguments = GetArguments();
            var requireAdmin = RequireAdminRights();

            if (string.IsNullOrWhiteSpace(executablePath))
            {
                return ToolResult.CreateFailure("可执行文件路径配置为空");
            }

            if (!System.IO.File.Exists(executablePath))
            {
                return ToolResult.CreateFailure($"可执行文件不存在: {executablePath}");
            }

            try
            {
                LogUserAction("ExecuteFile", $"Path: {executablePath}, Args: {arguments}, Admin: {requireAdmin}");

                await Task.Run(() =>
                {
                    var startInfo = new ProcessStartInfo
                    {
                        FileName = executablePath,
                        Arguments = arguments,
                        UseShellExecute = true
                    };

                    if (requireAdmin)
                    {
                        startInfo.Verb = "runas";
                    }

                    Process.Start(startInfo);
                });

                return ToolResult.CreateSuccess($"已启动 {Name}");
            }
            catch (System.ComponentModel.Win32Exception ex) when (ex.NativeErrorCode == 1223)
            {
                // 用户取消了UAC提示
                return ToolResult.CreateFailure("用户取消了权限提升请求");
            }
            catch (UnauthorizedAccessException ex)
            {
                return ToolResult.CreateFailure($"权限不足: {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                return ToolResult.CreateFailure($"启动程序失败: {ex.Message}", ex);
            }
        }

        protected override bool CanExecuteInternal(ToolParameters parameters)
        {
            var executablePath = GetExecutablePath();
            return !string.IsNullOrWhiteSpace(executablePath) && System.IO.File.Exists(executablePath);
        }

        public override ValidationResult ValidateConfiguration()
        {
            var executablePath = GetExecutablePath();
            
            if (string.IsNullOrWhiteSpace(executablePath))
            {
                return ValidationResult.Invalid($"工具 {Name} 的可执行文件路径配置为空");
            }

            if (!System.IO.File.Exists(executablePath))
            {
                return ValidationResult.Invalid($"工具 {Name} 的可执行文件不存在: {executablePath}");
            }

            return ValidationResult.Valid();
        }
    }
}
