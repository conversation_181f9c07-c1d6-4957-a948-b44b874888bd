﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="skinEngine1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAMDAAAAEAIACoJQAAFgAAACgAAAAwAAAAYAAAAAEAIAAAAAAAACQAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAjqynOIjE0seG0Of9hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW
        8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+xp///saf//7Gn//+xp///saf//7Gn
        //+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Ci
        8/2um97HrIyxOgAAAACOrKc4hs/k+4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW
        8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+xp///saf//7Gn
        //+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn
        //+xp///saf//7Gn//+xp///sKLy+6yMsTiIxNHHhdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW
        8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW
        8f+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn
        //+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//66b3seG0Of9hdbx/4XW8f+F1vH/hdbx/4XW
        8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW
        8f+F1vH/hdbx/4XW8f+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn
        //+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Ci8/2F1vH/hdbx/4XW
        8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW
        8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn
        //+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn
        //+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW
        8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+xp///saf//7Gn//+xp///saf//7Gn
        //+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn
        //+xp///saf//7Gn//+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW
        8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+xp///saf//7Gn
        //+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn
        //+xp///saf//7Gn//+xp///saf//7Gn//+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW
        8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW
        8f+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn
        //+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn//+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW
        8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW
        8f+F1vH/hdbx/4XW8f+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn
        //+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn//+F1vH/hdbx/4XW
        8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW
        8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn
        //+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn
        //+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW
        8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+xp///saf//7Gn//+xp///saf//7Gn
        //+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn
        //+xp///saf//7Gn//+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW
        8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+xp///saf//7Gn
        //+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn
        //+xp///saf//7Gn//+xp///saf//7Gn//+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW
        8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW
        8f+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn
        //+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn//+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW
        8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW
        8f+F1vH/hdbx/4XW8f+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn
        //+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn//+F1vH/hdbx/4XW
        8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW
        8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn
        //+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn
        //+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW
        8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+xp///saf//7Gn//+xp///saf//7Gn
        //+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn
        //+xp///saf//7Gn//+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW
        8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+xp///saf//7Gn
        //+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn
        //+xp///saf//7Gn//+xp///saf//7Gn//+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW
        8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW
        8f+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn
        //+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn//+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW
        8f+F1vH/wer4/8Hq+P/B6vj/wer3/4bW8P+F1vH/hdbw/8Dq9//B6vj/wer4/8Hq+P/A6vf/hdbx/4XW
        8f+X2e7/wer4/8Hq+P/X0///19P//87I+f+xp///saf//7Gn//+xp///saf//7Gn///V0Pz/19P//9fT
        ///X0///vrX6/7Gn//+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn//+F1vH/hdbx/4XW
        8f+F1vH/hdbx/4XW8f+F1vH//f////3////9/////f///4fW8P+F1vH/wOby//3////9/////f////3/
        ///9//7/hdbx/4XW8f/H6vT//f////3////9/////f////z+/v+0qvz/saf//7Gn//+xp///saf//7Gn
        ///5+vr//f////3////9////zMT1/7Gn//+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn
        //+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH//f////3////9/////f///4fW8P+U2O7/+/39//3/
        ///9/////f////3////9//7/hdbx/4XW8f/4+/r//f////3////9/////f////3////Uzvv/saf//7Gn
        //+xp///saf//7Gn///5+vr//f////3////9////y8L1/7Gn//+xp///saf//7Gn//+xp///saf//7Gn
        //+xp///saf//7Gn//+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH//f////3////9/////f///4fW
        8P/Z8Pb//f////3////9/////f////3////9//7/hdbx/7Lh8P/9/////f////3////9/////f////3/
        ///y8Pj/saf//7Gn//+xp///saf//7Gn///5+vr//f////3////9////y8L1/7Gn//+xp///saf//7Gn
        //+xp///saf//7Gn//+xp///saf//7Gn//+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW8f+F1vH//f////3/
        ///9/////f///6bf8f/9/////f////3////9/////f////3////9//7/hdbx/9zw9P/9/////f////3/
        ///9/////f////3////9////wbj5/7Gn//+xp///saf//7Gn///5+vr//f////3////9////y8L1/7Gn
        //+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn//+F1vH/hdbx/4XW8f+F1vH/hdbx/4XW
        8f+F1vH//f////3////9/////f////D3+P/9/////f////3////9/////f////3////9//7/kdfu//z+
        /v/9/////f////z+/v/n5vz//f////3////9////39v5/7Gn//+xp///saf//7Gn///5+vr//f////3/
        ///9////y8L1/7Gn//+xp///saf//7Gn//+xp///saf//7Gn//+xp///saf//7Gn//9Y4a3/WOGt/1jh
        rf9Y4a3/WOGt/1jhrf9Y4a3//f////3////9/////f////3////9/////f///+D16//9/////f////3/
        ///9/v7/qu7T//3////9/////f///9Dy4//215H//f////3////9/////Pz7//bRfP/30Xn/99F5//fR
        ef/7+vf//f////3////9////9tyj//fRef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fR
        ef9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3//f////3////9/////f////3////9////+/79/43i
        vP/9/////f////3////8/v7/8fn0//3////9/////f///5HnxP/30Xn/+fPk//3////9/////f////fh
        rv/30Xn/99F5//fRef/7+vb//f////3////9////9dug//fRef/30Xn/99F5//fRef/30Xn/99F5//fR
        ef/30Xn/99F5//fRef9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3//f////3////9/////f////3/
        ///9////tO3V/3vht//9/////f////3////9/////f////3////9////8vr1/1jhrf/30Xn/9+Kw//3/
        ///9/////f////z+/v/8/fz//P38//z9/P/9/////f////3////9/////P39//z9/P/8/fz//Pz7//fR
        ef/30Xn/99F5//fRef/30Xn/99F5//fRef9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3//f////3/
        ///9/////f////3////v+fT/WuGt/3vht//9/////f////3////9/////f////3////9////re7U/1jh
        rf/30Xn/9tF7//z8+//9/////f////3////9/////f////3////9/////f////3////9/////f////3/
        ///9/////f////fRef/30Xn/99F5//fRef/30Xn/99F5//fRef9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jh
        rf9Y4a3//f////3////9/////f////3///+N58P/WOGt/3vht//9/////f////3////9/////f////3/
        ///7/fz/a+Gy/1jhrf/30Xn/99F5//jowv/9/////f////3////9/////f////3////9/////f////3/
        ///9/////f////3////9/////f////fRef/30Xn/99F5//fRef/30Xn/99F5//fRef9Y4a3/WOGt/1jh
        rf9Y4a3/WOGt/1jhrf9Y4a3/qvDW/6rw1v+q8Nb/qvDW/6bt0P9Y4a3/WOGt/2jhsf+q8Nb/qvDW/6rw
        1v+q8Nb/qvDW/6rw1v9+5r3/WOGt/1jhrf/30Xn/99F5//fRef/45Lb/+ui8//rovP/66Lz/+ui8//ro
        vP/66Lz/+ui8//rovP/66Lz/+ui8//rovP/66Lz/+ui8//fRef/30Xn/99F5//fRef/30Xn/99F5//fR
        ef9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jh
        rf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf/30Xn/99F5//fRef/30Xn/99F5//fR
        ef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fR
        ef/30Xn/99F5//fRef9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jh
        rf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf/30Xn/99F5//fR
        ef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fR
        ef/30Xn/99F5//fRef/30Xn/99F5//fRef9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jh
        rf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jh
        rf/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fR
        ef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fRef9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jh
        rf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jh
        rf9Y4a3/WOGt/1jhrf/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fR
        ef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fRef9Y4a3/WOGt/1jh
        rf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jh
        rf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fR
        ef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fR
        ef9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jh
        rf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf/30Xn/99F5//fRef/30Xn/99F5//fR
        ef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fR
        ef/30Xn/99F5//fRef9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jh
        rf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf/30Xn/99F5//fR
        ef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fR
        ef/30Xn/99F5//fRef/30Xn/99F5//fRef9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jh
        rf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jh
        rf/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fR
        ef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fRef9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jh
        rf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jh
        rf9Y4a3/WOGt/1jhrf/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fR
        ef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fRef9Y4a3/WOGt/1jh
        rf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jh
        rf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fR
        ef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fR
        ef9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jh
        rf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf/30Xn/99F5//fRef/30Xn/99F5//fR
        ef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fR
        ef/30Xn/99F5//fRef9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jh
        rf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf/30Xn/99F5//fR
        ef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fR
        ef/30Xn/99F5//fRef/30Xn/99F5//fRef9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jh
        rf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jh
        rf/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fR
        ef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fRef9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jh
        rf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jh
        rf9Y4a3/WOGt/1jhrf/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fR
        ef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fRef9a26b9WOGt/1jh
        rf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jh
        rf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fR
        ef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//PL
        dP1hzpbHWOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jh
        rf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf/30Xn/99F5//fRef/30Xn/99F5//fR
        ef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fR
        ef/30Xn/99F5/+zAacdutHg4W9mk+1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jh
        rf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf/30Xn/99F5//fR
        ef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fR
        ef/30Xn/99F5//fRef/30Xn/8spz+92oUzgAAAAAbrR4OGHOl8Va26X9WOGt/1jhrf9Y4a3/WOGt/1jh
        rf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jhrf9Y4a3/WOGt/1jh
        rf/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fRef/30Xn/99F5//fR
        ef/30Xn/99F5//fRef/30Xn/99F5//PLdP3swGnF3ahUOgAAAADAAAAAAAMAAIAAAAAAAQAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIAAAAAAAQAAwAAAAAAD
        AAA=
</value>
  </data>
</root>