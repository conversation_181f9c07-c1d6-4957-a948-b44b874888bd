using System;
using System.Collections.Generic;

namespace 工具箱.Core.Interfaces
{
    /// <summary>
    /// 配置服务接口，提供统一的配置管理
    /// </summary>
    public interface IConfigurationService
    {
        /// <summary>
        /// 获取配置值
        /// </summary>
        /// <typeparam name="T">配置值类型</typeparam>
        /// <param name="key">配置键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>配置值</returns>
        T GetValue<T>(string key, T defaultValue = default(T));

        /// <summary>
        /// 设置配置值
        /// </summary>
        /// <typeparam name="T">配置值类型</typeparam>
        /// <param name="key">配置键</param>
        /// <param name="value">配置值</param>
        void SetValue<T>(string key, T value);

        /// <summary>
        /// 检查配置键是否存在
        /// </summary>
        /// <param name="key">配置键</param>
        /// <returns>是否存在</returns>
        bool <PERSON>ey(string key);

        /// <summary>
        /// 删除配置项
        /// </summary>
        /// <param name="key">配置键</param>
        /// <returns>是否成功删除</returns>
        bool RemoveKey(string key);

        /// <summary>
        /// 获取所有配置键
        /// </summary>
        /// <returns>配置键集合</returns>
        IEnumerable<string> GetAllKeys();

        /// <summary>
        /// 获取指定前缀的所有配置
        /// </summary>
        /// <param name="prefix">键前缀</param>
        /// <returns>配置字典</returns>
        Dictionary<string, object> GetSection(string prefix);

        /// <summary>
        /// 保存配置到持久化存储
        /// </summary>
        void Save();

        /// <summary>
        /// 从持久化存储重新加载配置
        /// </summary>
        void Reload();

        /// <summary>
        /// 重置所有配置为默认值
        /// </summary>
        void Reset();

        /// <summary>
        /// 配置变更事件
        /// </summary>
        event EventHandler<ConfigurationChangedEventArgs> ConfigurationChanged;
    }

    /// <summary>
    /// 配置变更事件参数
    /// </summary>
    public class ConfigurationChangedEventArgs : EventArgs
    {
        public string Key { get; set; }
        public object OldValue { get; set; }
        public object NewValue { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 配置节接口，用于强类型配置
    /// </summary>
    public interface IConfigurationSection
    {
        /// <summary>
        /// 节名称
        /// </summary>
        string SectionName { get; }

        /// <summary>
        /// 从配置服务加载配置
        /// </summary>
        /// <param name="configService">配置服务</param>
        void LoadFrom(IConfigurationService configService);

        /// <summary>
        /// 保存配置到配置服务
        /// </summary>
        /// <param name="configService">配置服务</param>
        void SaveTo(IConfigurationService configService);

        /// <summary>
        /// 验证配置
        /// </summary>
        /// <returns>验证结果</returns>
        ValidationResult Validate();
    }
}
