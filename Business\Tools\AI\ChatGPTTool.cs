using System.Threading.Tasks;
using 工具箱.Core.Interfaces;
using 工具箱.Core.Models;

namespace 工具箱.Business.Tools.AI
{
    /// <summary>
    /// ChatGPT工具实现
    /// </summary>
    public class ChatGPTTool : UrlBaseTool
    {
        public ChatGPTTool(ILoggingService logger, IConfigurationService configService)
            : base(logger, configService)
        {
            Icon = "🧠";
        }

        public override string Name => "ChatGPT";

        public override string Description => "智能AI对话助手，支持多种任务处理";

        public override ToolCategory Category => ToolCategory.AI工具;

        protected override string GetUrl()
        {
            return GetConfigValue("Tools.AI.ChatGPT.Url", "https://fastgpt.atlbattery.com/chat/share?shareId=652f6aaec6e21c41b67348d5");
        }

        public override ValidationResult ValidateConfiguration()
        {
            var baseValidation = base.ValidateConfiguration();
            if (!baseValidation.IsValid)
                return baseValidation;

            var url = GetUrl();
            if (url.Contains("fastgpt.atlbattery.com"))
            {
                return ValidationResult.Valid();
            }

            return ValidationResult.Invalid("ChatGPT URL配置不正确");
        }
    }

    /// <summary>
    /// DeepSeek工具实现
    /// </summary>
    public class DeepSeekTool : UrlBaseTool
    {
        public DeepSeekTool(ILoggingService logger, IConfigurationService configService)
            : base(logger, configService)
        {
            Icon = "🤖";
        }

        public override string Name => "DeepSeek";

        public override string Description => "DeepSeek AI智能助手";

        public override ToolCategory Category => ToolCategory.AI工具;

        protected override string GetUrl()
        {
            return GetConfigValue("Tools.AI.DeepSeek.Url", "https://chat.deepseek.com/sign_in");
        }
    }

    /// <summary>
    /// PPT助手工具实现
    /// </summary>
    public class PPTAssistantTool : UrlBaseTool
    {
        public PPTAssistantTool(ILoggingService logger, IConfigurationService configService)
            : base(logger, configService)
        {
            Icon = "📊";
        }

        public override string Name => "PPT助手";

        public override string Description => "AI驱动的PPT制作助手";

        public override ToolCategory Category => ToolCategory.AI工具;

        protected override string GetUrl()
        {
            return GetConfigValue("Tools.AI.PPTAssistant.Url", "https://kimi.moonshot.cn/kimiplus/cvvm7bkheutnihqi2100");
        }
    }

    /// <summary>
    /// 小浣熊工具实现
    /// </summary>
    public class XiaoHuanXiongTool : UrlBaseTool
    {
        public XiaoHuanXiongTool(ILoggingService logger, IConfigurationService configService)
            : base(logger, configService)
        {
            Icon = "🦝";
        }

        public override string Name => "小浣熊";

        public override string Description => "小浣熊AI助手";

        public override ToolCategory Category => ToolCategory.AI工具;

        protected override string GetUrl()
        {
            return GetConfigValue("Tools.AI.XiaoHuanXiong.Url", "https://xiaohuanxiong.com/");
        }
    }

    /// <summary>
    /// 数字人生成工具实现
    /// </summary>
    public class DigitalHumanTool : UrlBaseTool
    {
        public DigitalHumanTool(ILoggingService logger, IConfigurationService configService)
            : base(logger, configService)
        {
            Icon = "👤";
        }

        public override string Name => "数字人生成";

        public override string Description => "AI数字人生成工具 (有言3D)";

        public override ToolCategory Category => ToolCategory.AI工具;

        protected override string GetUrl()
        {
            return GetConfigValue("Tools.AI.DigitalHuman.Url", "https://www.youyan3d.com/platform?from=drlx_yhaigc");
        }
    }

    /// <summary>
    /// 文生图工具实现
    /// </summary>
    public class TextToImageTool : UrlBaseTool
    {
        public TextToImageTool(ILoggingService logger, IConfigurationService configService)
            : base(logger, configService)
        {
            Icon = "🎨";
        }

        public override string Name => "文生图";

        public override string Description => "AI文本生成图片工具 (LiblibAI)";

        public override ToolCategory Category => ToolCategory.AI工具;

        protected override string GetUrl()
        {
            return GetConfigValue("Tools.AI.TextToImage.Url", "https://www.liblib.art/?sourceId=000147&qhclickid=e2dba2386860b107");
        }
    }

    /// <summary>
    /// 文生视频工具实现
    /// </summary>
    public class TextToVideoTool : UrlBaseTool
    {
        public TextToVideoTool(ILoggingService logger, IConfigurationService configService)
            : base(logger, configService)
        {
            Icon = "🎬";
        }

        public override string Name => "文生视频";

        public override string Description => "AI文本生成视频工具 (可灵AI)";

        public override ToolCategory Category => ToolCategory.AI工具;

        protected override string GetUrl()
        {
            return GetConfigValue("Tools.AI.TextToVideo.Url", "https://app.klingai.com/cn/activity-zone?id=726928327164321859");
        }
    }

    /// <summary>
    /// 文生声音工具实现
    /// </summary>
    public class TextToVoiceTool : UrlBaseTool
    {
        public TextToVoiceTool(ILoggingService logger, IConfigurationService configService)
            : base(logger, configService)
        {
            Icon = "🎵";
        }

        public override string Name => "文生声音";

        public override string Description => "AI文本生成语音工具";

        public override ToolCategory Category => ToolCategory.AI工具;

        protected override string GetUrl()
        {
            return GetConfigValue("Tools.AI.TextToVoice.Url", "https://noiz.ai/landing");
        }
    }

    /// <summary>
    /// 文生PPT工具实现
    /// </summary>
    public class TextToPPTTool : UrlBaseTool
    {
        public TextToPPTTool(ILoggingService logger, IConfigurationService configService)
            : base(logger, configService)
        {
            Icon = "📄";
            IsEnabled = false; // 默认禁用，因为还在开发中
        }

        public override string Name => "文生PPT";

        public override string Description => "AI文本生成PPT工具 (开发中)";

        public override ToolCategory Category => ToolCategory.AI工具;

        protected override string GetUrl()
        {
            return GetConfigValue("Tools.AI.TextToPPT.Url", "待开发");
        }

        protected override bool CanExecuteInternal(ToolParameters parameters)
        {
            // 开发中的功能不允许执行
            return false;
        }

        protected override Task<ToolResult> ExecuteInternal(ToolParameters parameters)
        {
            return Task.FromResult(ToolResult.CreateFailure("该功能正在开发中，敬请期待"));
        }
    }
}
