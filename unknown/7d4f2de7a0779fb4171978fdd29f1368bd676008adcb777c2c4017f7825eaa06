using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using AntdUI;
using 工具箱.Core.Interfaces;
using 工具箱.Presentation.Services;

namespace 工具箱
{
    public partial class Form1 : Form
    {
        private readonly ApplicationService _applicationService;
        private readonly ILoggingService _logger;
        private readonly IToolManager _toolManager;

        public Form1(ApplicationService applicationService)
        {
            _applicationService = applicationService ?? throw new ArgumentNullException(nameof(applicationService));
            _logger = _applicationService.Logger;
            _toolManager = _applicationService.ToolManager;

            InitializeComponent();

            // 设置 DPI 感知
            this.AutoScaleMode = AutoScaleMode.Dpi;

            // 应用主题配置
            ApplyThemeConfiguration();

            // 绑定按钮事件（使用新的统一处理方式）
            BindButtonEvents();

            // 只在运行时执行初始化，避免在设计时干扰设计器
            if (!this.DesignMode)
            {
                // 初始化 Select 组件
                InitializeSelectFunction();

                // 初始化侧边栏
                InitializeSidebar();

                // 注意：通知信息将在Form_Load事件中异步加载

                // 绑定帮助按钮事件
                this.HelpButtonClicked += Form1_HelpButtonClicked;

                // 绑定窗体加载事件
                this.Load += Form1_Load;

                // 绑定窗体关闭事件
                this.FormClosing += Form1_FormClosing;
            }

            _logger.LogInfo("主窗体初始化完成");
        }

        /// <summary>
        /// 应用主题配置
        /// </summary>
        private void ApplyThemeConfiguration()
        {
            try
            {
                var theme = _applicationService.AppConfig.UI.Theme;
                if (!string.IsNullOrEmpty(theme))
                {
                    this.skinEngine1.SkinFile = theme;
                }
            }
            catch (Exception ex)
            {
                _applicationService.HandleException(ex, "应用主题配置", false);
                // 使用默认主题
                this.skinEngine1.SkinFile = "RealOne.ssk";
            }
        }

        /// <summary>
        /// 绑定按钮事件
        /// </summary>
        private void BindButtonEvents()
        {
            try
            {
                // 使用统一的工具执行方法绑定所有按钮
                this.btnChatGPT.Click += async (s, e) => await ExecuteToolAsync("ChatGPT");
                this.btnNvtOa.Click += async (s, e) => await ExecuteToolAsync("NVT-OA");
                this.btnBaidu.Click += async (s, e) => await ExecuteToolAsync("百度");
                this.btnReleaseMemory.Click += async (s, e) => await ExecuteToolAsync("释放系统内存");
                this.btnSearchFiles.Click += async (s, e) => await ExecuteToolAsync("搜索文件");
                this.btnChangePassword.Click += async (s, e) => await ExecuteToolAsync("修改计算机密码");
                this.btnPrivacyLock.Click += async (s, e) => await ExecuteToolAsync("FineBI商业智能");
                this.btnFileConvert.Click += async (s, e) => await ExecuteToolAsync("文件转换");
                this.btnDeepSeek.Click += async (s, e) => await ExecuteToolAsync("DeepSeek");
                this.btnMindMap.Click += async (s, e) => await ExecuteToolAsync("思维导图");
                this.btnActivationTool.Click += async (s, e) => await ExecuteToolAsync("激活工具");
                this.btnIPInfo.Click += async (s, e) => await ExecuteToolAsync("IP信息查看");

                // AI工具按钮
                this.btnPPTAssistant.Click += async (s, e) => await ExecuteToolAsync("PPT助手");
                this.btnXiaoHuanXiong.Click += async (s, e) => await ExecuteToolAsync("小浣熊");
                this.btnDigitalHuman.Click += async (s, e) => await ExecuteToolAsync("数字人生成");
                this.btnTextToImage.Click += async (s, e) => await ExecuteToolAsync("文生图");
                this.btnTextToVideo.Click += async (s, e) => await ExecuteToolAsync("文生视频");
                this.btnTextToVoice.Click += async (s, e) => await ExecuteToolAsync("文生声音");
                this.btnTextToPPT.Click += async (s, e) => await ExecuteToolAsync("文生PPT");
            }
            catch (Exception ex)
            {
                _applicationService.HandleException(ex, "绑定按钮事件");
            }
        }

        /// <summary>
        /// 统一的工具执行方法
        /// </summary>
        /// <param name="toolName">工具名称</param>
        private async Task ExecuteToolAsync(string toolName)
        {
            try
            {
                // 显示执行提示
                AntdUI.Message.info(this, $"正在启动 {toolName}...", null, 1);

                // 执行工具
                var result = await _applicationService.ExecuteToolAsync(toolName);

                if (result.Success)
                {
                    // 成功提示（如果有消息）
                    if (!string.IsNullOrEmpty(result.Message))
                    {
                        AntdUI.Message.success(this, result.Message, null, 1);
                    }
                }
                else
                {
                    // 错误提示
                    AntdUI.Message.error(this, result.Message ?? "工具执行失败", null, 3);
                }
            }
            catch (Exception ex)
            {
                _applicationService.HandleException(ex, $"执行工具 {toolName}");
            }
        }

        /// <summary>
        /// 窗体关闭事件处理
        /// </summary>
        private void Form1_FormClosing(object sender, FormClosingEventArgs e)
        {
            try
            {
                _logger.LogInfo("主窗体正在关闭");

                // 保存窗体状态到配置
                SaveWindowState();
            }
            catch (Exception ex)
            {
                _applicationService.HandleException(ex, "窗体关闭", false);
            }
        }

        /// <summary>
        /// 保存窗体状态
        /// </summary>
        private void SaveWindowState()
        {
            try
            {
                var config = _applicationService.AppConfig.UI;

                if (this.WindowState == FormWindowState.Normal)
                {
                    config.WindowSize.Width = this.Width;
                    config.WindowSize.Height = this.Height;
                    config.WindowPosition.X = this.Left;
                    config.WindowPosition.Y = this.Top;
                }

                config.StartMaximized = this.WindowState == FormWindowState.Maximized;

                _applicationService.SaveConfiguration();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存窗体状态失败");
            }
        }



        // 注意：原有的按钮点击事件处理方法已被统一的 ExecuteToolAsync 方法替代



        // 注意：密码修改相关的方法已迁移到 ChangePasswordTool 类中

        // 注意：这些方法已被统一的 ExecuteToolAsync 方法替代

        // 注意：激活工具相关的方法已迁移到 ActivationTool 类中

        // 注意：IP信息相关的方法已迁移到 IPInfoTool 类中

        // 注意：AI工具相关的方法已迁移到对应的工具类中

        /// <summary>
        /// 帮助按钮点击事件处理程序
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">帮助事件参数</param>
        private void Form1_HelpButtonClicked(object sender, System.ComponentModel.CancelEventArgs e)
        {
            // 取消默认的帮助行为
            e.Cancel = true;

            // 显示自定义帮助信息
            ShowHelpDialog();
        }

        /// <summary>
        /// 显示帮助对话框
        /// </summary>
        private void ShowHelpDialog()
        {
            try
            {
                // 使用 AntdUI.Message 显示简短提示
                AntdUI.Message.info(this, "显示帮助信息...", null, 1);

                // 构建帮助信息内容
                string helpContent =
                    "=== 关于工具箱 ===\n\n" +
                    "此集成工具箱为NVT桌面运维独立开发\n\n" +
                    "如有问题或使用中出现问题请联系相关人员\n\n" +
                    "=== 使用说明 ===\n\n" +
                    "• 使用顶部搜索框可以快速查找功能\n" +
                    "• 点击对应按钮即可使用相关工具\n" +
                    "• 如果按钮较多，可以滚动查看更多功能\n\n" +
                    "版本信息：v1.0\n" +
                    "开发团队：NVT桌面运维";

                // 显示帮助对话框
                MessageBox.Show(
                    helpContent,
                    "关于工具箱",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                // 显示成功提示
                AntdUI.Message.success(this, "帮助信息已显示", null, 1);
            }
            catch (Exception ex)
            {
                // 错误处理
                AntdUI.Message.error(this, "显示帮助信息失败", null, 1);
                MessageBox.Show(
                    $"显示帮助信息时发生错误：\n{ex.Message}",
                    "错误",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }







        // 注意：OpenUrl 方法已被工具类中的统一URL处理替代

        private void InitializeSelectFunction()
        {
            // 清空 Items，不需要下拉选项
            selectFunction.Items.Clear();

            // 禁用下拉功能
            selectFunction.DropDownArrow = false;
            selectFunction.ClickSwitchDropdown = false;
            selectFunction.MaxCount = 0;

            // 绑定事件
            selectFunction.TextChanged += SelectFunction_TextChanged;
        }

        /// <summary>
        /// 初始化侧边栏
        /// </summary>
        private void InitializeSidebar()
        {
            try
            {
                // 确保侧边栏面板可见
                sidebarPanel.Visible = true;
                sidebarPanel.BringToFront();

                // 使用设计器中设置的大小，不强制覆盖
                // 只确保 TreeView 的位置正确（在 sidebarLabel 下方）
                int labelHeight = sidebarLabel.Height;
                if (toolsTreeView.Location.Y != labelHeight)
                {
                    toolsTreeView.Location = new Point(0, labelHeight);
                }

                // 清空树形控件（包括设计时添加的节点）
                toolsTreeView.Nodes.Clear();

                // 创建AI工具分类
                TreeNode AI工具Node = new TreeNode("🤖 AI工具") { Tag = "AI工具" };
                AI工具Node.Nodes.Add(new TreeNode("🧠 DeepSeek") { Tag = "DeepSeek" });
                AI工具Node.Nodes.Add(new TreeNode("💬 ChatGPT") { Tag = "ChatGPT" });
                AI工具Node.Nodes.Add(new TreeNode("📊 PPT助手") { Tag = "PPTAssistant" });
                AI工具Node.Nodes.Add(new TreeNode("🦝 小浣熊") { Tag = "XiaoHuanXiong" });
                AI工具Node.Nodes.Add(new TreeNode("👤 数字人生成") { Tag = "DigitalHuman" });
                AI工具Node.Nodes.Add(new TreeNode("🎨 文生图") { Tag = "TextToImage" });
                AI工具Node.Nodes.Add(new TreeNode("🎬 文生视频") { Tag = "TextToVideo" });
                AI工具Node.Nodes.Add(new TreeNode("🎵 文生声音") { Tag = "TextToVoice" });
                AI工具Node.Nodes.Add(new TreeNode("📄 文生PPT") { Tag = "TextToPPT" });

                // 创建系统工具分类
                TreeNode 系统工具Node = new TreeNode("⚙️ 系统工具") { Tag = "系统工具" };
                系统工具Node.Nodes.Add(new TreeNode("🧹 释放系统内存") { Tag = "ReleaseMemory" });
                系统工具Node.Nodes.Add(new TreeNode("🔐 修改计算机密码") { Tag = "ChangePassword" });
                系统工具Node.Nodes.Add(new TreeNode("🌐 IP信息查看") { Tag = "IPInfo" });
                系统工具Node.Nodes.Add(new TreeNode("🔑 激活工具") { Tag = "ActivationTool" });

                // 创建网络工具分类
                TreeNode 网络工具Node = new TreeNode("🌍 网络工具") { Tag = "网络工具" };
                网络工具Node.Nodes.Add(new TreeNode("🏢 NVT-OA") { Tag = "NvtOa" });
                网络工具Node.Nodes.Add(new TreeNode("🔍 百度") { Tag = "Baidu" });
                网络工具Node.Nodes.Add(new TreeNode("🔄 文件转换") { Tag = "FileConvert" });

                // 创建实用工具分类
                TreeNode 实用工具Node = new TreeNode("🛠️ 实用工具") { Tag = "实用工具" };
                实用工具Node.Nodes.Add(new TreeNode("📁 搜索文件") { Tag = "SearchFiles" });
                实用工具Node.Nodes.Add(new TreeNode("🗺️ 思维导图") { Tag = "MindMap" });
                实用工具Node.Nodes.Add(new TreeNode("📈 FineBI商业智能") { Tag = "PrivacyLock" });

                // 创建"显示全部"节点
                TreeNode 显示全部Node = new TreeNode("📋 显示全部") { Tag = "显示全部" };

                // 添加所有分类到树形控件
                toolsTreeView.Nodes.Add(显示全部Node);
                toolsTreeView.Nodes.Add(AI工具Node);
                toolsTreeView.Nodes.Add(系统工具Node);
                toolsTreeView.Nodes.Add(网络工具Node);
                toolsTreeView.Nodes.Add(实用工具Node);

                // 默认选中"显示全部"节点
                toolsTreeView.SelectedNode = 显示全部Node;

                // 设置现代化的树形控件样式
                toolsTreeView.ItemHeight = 40;
                toolsTreeView.Indent = 20;
                toolsTreeView.ShowLines = false;
                toolsTreeView.ShowPlusMinus = true;
                toolsTreeView.ShowRootLines = false;
                toolsTreeView.HotTracking = false;
                toolsTreeView.BackColor = Color.FromArgb(248, 249, 250);

                // 设置支持 Emoji 的字体
                try
                {
                    toolsTreeView.Font = new Font("Segoe UI Emoji", 10f, FontStyle.Regular);
                }
                catch
                {
                    try
                    {
                        toolsTreeView.Font = new Font("Microsoft YaHei UI", 10f, FontStyle.Regular);
                    }
                    catch
                    {
                        try
                        {
                            toolsTreeView.Font = new Font("微软雅黑", 10f, FontStyle.Regular);
                        }
                        catch
                        {
                            // 如果都不可用，保持默认字体
                            System.Diagnostics.Debug.WriteLine("无法设置支持 Emoji 的字体，使用默认字体");
                        }
                    }
                }

                // 绑定树形控件事件
                toolsTreeView.NodeMouseClick += ToolsTreeView_NodeMouseClick;

                // 暂时禁用自定义绘制来测试
                // 只在运行时启用自定义绘制
                //if (!this.DesignMode)
                //{
                //    toolsTreeView.DrawMode = TreeViewDrawMode.OwnerDrawText;
                //    toolsTreeView.DrawNode += ToolsTreeView_DrawNode;
                //}

                // 确保控件可见
                toolsTreeView.Visible = true;
                sidebarLabel.Visible = true;

                // 默认显示所有按钮
                显示全部Buttons();

                // 检查 Emoji 显示是否正常，如果不正常则使用文本替代
                CheckAndFixEmojiDisplay();

                // 验证按钮是否存在
                ValidateButtons();

                System.Diagnostics.Debug.WriteLine("侧边栏初始化完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"侧边栏初始化失败: {ex.Message}");
                MessageBox.Show($"侧边栏初始化失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 验证所有按钮是否存在
        /// </summary>
        private void ValidateButtons()
        {
            var buttonNames = new[]
            {
                "btnDeepSeek", "btnChatGPT", "btnPPTAssistant", "btnXiaoHuanXiong", "btnDigitalHuman", "btnTextToImage", "btnTextToVideo", "btnTextToVoice", "btnTextToPPT",
                "btnReleaseMemory", "btnChangePassword", "btnIPInfo", "btnActivationTool",
                "btnNvtOa", "btnBaidu", "btnFileConvert",
                "btnSearchFiles", "btnMindMap", "btnPrivacyLock"
            };

            var buttons = new[]
            {
                btnDeepSeek, btnChatGPT, btnPPTAssistant, btnXiaoHuanXiong, btnDigitalHuman, btnTextToImage, btnTextToVideo, btnTextToVoice, btnTextToPPT,
                btnReleaseMemory, btnChangePassword, btnIPInfo, btnActivationTool,
                btnNvtOa, btnBaidu, btnFileConvert,
                btnSearchFiles, btnMindMap, btnPrivacyLock
            };

            for (int i = 0; i < buttons.Length; i++)
            {
                if (buttons[i] == null)
                {
                    System.Diagnostics.Debug.WriteLine($"警告: 按钮 {buttonNames[i]} 为 null");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"按钮 {buttonNames[i]} 存在");
                }
            }
        }

        /// <summary>
        /// 窗体加载事件处理程序
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private async void Form1_Load(object sender, EventArgs e)
        {
            try
            {
                // 强制刷新布局
                this.PerformLayout();
                sidebarPanel.PerformLayout();

                // 确保侧边栏显示正确
                sidebarPanel.Invalidate();
                toolsTreeView.Invalidate();

                // 确保标题没有下划线
                label1.Font = new Font("幼圆", 15F, FontStyle.Bold, GraphicsUnit.Point);

                // 异步加载通知信息
                await LoadNotificationTextAsync();

                _logger.LogInfo("窗体加载完成，布局已刷新");
            }
            catch (Exception ex)
            {
                _applicationService.HandleException(ex, "窗体加载", false);
            }
        }







        /// <summary>
        /// 显示所有控件
        /// </summary>
        private void 显示全部Controls()
        {
            // 暂停布局更新以提高性能
            tableLayoutPanel1.SuspendLayout();

            try
            {
                // 遍历 tableLayoutPanel1 中的所有控件，设置为可见
                foreach (Control control in tableLayoutPanel1.Controls)
                {
                    control.Visible = true;
                }
            }
            finally
            {
                // 恢复布局更新
                tableLayoutPanel1.ResumeLayout(true);
            }
        }

        /// <summary>
        /// Select 组件文本变化事件处理程序 - 实现实时搜索过滤
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void SelectFunction_TextChanged(object sender, EventArgs e)
        {
            string searchText = selectFunction.Text?.Trim() ?? "";

            // 如果搜索文本为空，显示所有按钮
            if (string.IsNullOrWhiteSpace(searchText))
            {
                显示全部Buttons();
                // 重置滚动位置到顶部
                scrollPanel.AutoScrollPosition = new Point(0, 0);
                return;
            }

            // 根据搜索文本实时过滤控件显示
            FilterControlsBySearchText(searchText);
        }

        /// <summary>
        /// 根据搜索文本过滤控件显示
        /// </summary>
        /// <param name="searchText">搜索文本</param>
        private void FilterControlsBySearchText(string searchText)
        {
            // 暂停布局更新以提高性能
            tableLayoutPanel1.SuspendLayout();

            try
            {
                // 收集匹配的按钮
                var matchedButtons = new List<AntdUI.Button>();

                // 遍历 tableLayoutPanel1 中的所有控件
                foreach (Control control in tableLayoutPanel1.Controls)
                {
                    bool shouldShow = false;

                    // 检查是否为 AntdUI.Button 控件
                    if (control is AntdUI.Button button)
                    {
                        // 获取按钮的文本
                        string buttonText = button.Text ?? "";

                        // 进行模糊匹配（不区分大小写）
                        shouldShow = buttonText.IndexOf(searchText, StringComparison.OrdinalIgnoreCase) >= 0;
                        
                        if (shouldShow)
                        {
                            matchedButtons.Add(button);
                        }
                    }
                    else
                    {
                        // 对于非按钮控件，也可以进行文本匹配（如果有Text属性）
                        var textProperty = control.GetType().GetProperty("Text");
                        if (textProperty != null)
                        {
                            string controlText = textProperty.GetValue(control)?.ToString() ?? "";
                            shouldShow = controlText.IndexOf(searchText, StringComparison.OrdinalIgnoreCase) >= 0;
                        }
                    }

                    // 先隐藏所有控件
                    control.Visible = false;
                }

                // 如果有匹配的按钮，重新排列它们从第一排第一个位置开始显示
                if (matchedButtons.Count > 0)
                {
                    RearrangeButtons(matchedButtons);
                }

                // 重置滚动位置到顶部
                scrollPanel.AutoScrollPosition = new Point(0, 0);
            }
            finally
            {
                // 恢复布局更新
                tableLayoutPanel1.ResumeLayout(true);
            }
        }





        /// <summary>
        /// 异步加载通知文本
        /// </summary>
        private async Task LoadNotificationTextAsync()
        {
            try
            {
                var notificationText = await _applicationService.GetNotificationTextAsync();
                alertNotification.Text = notificationText;
            }
            catch (Exception ex)
            {
                _applicationService.HandleException(ex, "加载通知信息", false);
                alertNotification.Text = "加载通知信息失败";
            }
        }

        /// <summary>
        /// 动态添加按钮到 TableLayoutPanel
        /// </summary>
        /// <param name="buttonText">按钮文本</param>
        /// <param name="clickHandler">点击事件处理方法</param>
        public void AddButton(string buttonText, EventHandler clickHandler)
        {
            // 计算当前按钮数量
            int currentButtonCount = tableLayoutPanel1.Controls.Count;
            int columns = tableLayoutPanel1.ColumnCount;

            // 计算新按钮的位置
            int row = currentButtonCount / columns;
            int col = currentButtonCount % columns;

            // 如果需要新行，增加行数
            if (row >= tableLayoutPanel1.RowCount)
            {
                tableLayoutPanel1.RowCount = row + 1;
                tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 100F));
            }

            // 创建新按钮
            var newButton = new AntdUI.Button
            {
                Text = buttonText,
                Dock = DockStyle.Fill,
                TabIndex = currentButtonCount + 20 // 避免与现有按钮冲突
            };

            // 绑定点击事件
            if (clickHandler != null)
            {
                newButton.Click += clickHandler;
            }

            // 添加到 TableLayoutPanel
            tableLayoutPanel1.Controls.Add(newButton, col, row);
        }

        /// <summary>
        /// 获取当前 TableLayoutPanel 中的按钮数量
        /// </summary>
        /// <returns>按钮数量</returns>
        public int GetButtonCount()
        {
            return tableLayoutPanel1.Controls.Count;
        }

        /// <summary>
        /// 清除所有动态添加的按钮（保留原有按钮）
        /// </summary>
        public void ClearDynamicButtons()
        {
            // 这里可以根据需要实现清除逻辑
            // 例如，只保留前11个按钮（当前的固定按钮）
            var controlsToRemove = new List<Control>();

            for (int i = 11; i < tableLayoutPanel1.Controls.Count; i++)
            {
                controlsToRemove.Add(tableLayoutPanel1.Controls[i]);
            }

            foreach (var control in controlsToRemove)
            {
                tableLayoutPanel1.Controls.Remove(control);
                control.Dispose();
            }
        }

        /// <summary>
        /// 树形控件节点点击事件处理程序
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private async void ToolsTreeView_NodeMouseClick(object sender, TreeNodeMouseClickEventArgs e)
        {
            // 处理"显示全部"节点的点击
            if (e.Node.Tag != null && e.Node.Tag.ToString() == "显示全部")
            {
                Handle显示全部Click();
                return;
            }

            // 处理分类节点的点击（有子节点的节点）
            if (e.Node.Nodes.Count > 0)
            {
                // 优先使用 Tag 属性，如果没有则使用文本
                string categoryIdentifier = e.Node.Tag?.ToString() ?? e.Node.Text;
                HandleCategoryClick(categoryIdentifier);
                return;
            }

            // 处理工具项目节点的点击（叶子节点）
            if (e.Node.Tag == null) return;

            string toolTag = e.Node.Tag.ToString();
            string toolName = e.Node.Text;

            try
            {
                // 创建工具标签到工具名称的映射
                var toolMapping = new Dictionary<string, string>
                {
                    // AI工具
                    { "DeepSeek", "DeepSeek" },
                    { "ChatGPT", "ChatGPT" },
                    { "PPTAssistant", "PPT助手" },
                    { "XiaoHuanXiong", "小浣熊" },
                    { "DigitalHuman", "数字人生成" },
                    { "TextToImage", "文生图" },
                    { "TextToVideo", "文生视频" },
                    { "TextToVoice", "文生声音" },
                    { "TextToPPT", "文生PPT" },

                    // 系统工具
                    { "ReleaseMemory", "释放系统内存" },
                    { "ChangePassword", "修改计算机密码" },
                    { "IPInfo", "IP信息查看" },
                    { "ActivationTool", "激活工具" },

                    // 网络工具
                    { "NvtOa", "NVT-OA" },
                    { "Baidu", "百度" },
                    { "FileConvert", "文件转换" },

                    // 实用工具
                    { "SearchFiles", "搜索文件" },
                    { "MindMap", "思维导图" },
                    { "PrivacyLock", "FineBI商业智能" }
                };

                // 获取对应的按钮并高亮显示
                var buttonMapping = new Dictionary<string, AntdUI.Button>
                {
                    { "DeepSeek", btnDeepSeek },
                    { "ChatGPT", btnChatGPT },
                    { "PPTAssistant", btnPPTAssistant },
                    { "XiaoHuanXiong", btnXiaoHuanXiong },
                    { "DigitalHuman", btnDigitalHuman },
                    { "TextToImage", btnTextToImage },
                    { "TextToVideo", btnTextToVideo },
                    { "TextToVoice", btnTextToVoice },
                    { "TextToPPT", btnTextToPPT },
                    { "ReleaseMemory", btnReleaseMemory },
                    { "ChangePassword", btnChangePassword },
                    { "IPInfo", btnIPInfo },
                    { "ActivationTool", btnActivationTool },
                    { "NvtOa", btnNvtOa },
                    { "Baidu", btnBaidu },
                    { "FileConvert", btnFileConvert },
                    { "SearchFiles", btnSearchFiles },
                    { "MindMap", btnMindMap },
                    { "PrivacyLock", btnPrivacyLock }
                };

                if (toolMapping.TryGetValue(toolTag, out var actualToolName))
                {
                    // 高亮对应的按钮
                    if (buttonMapping.TryGetValue(toolTag, out var button))
                    {
                        HighlightButton(button);
                    }

                    // 使用新的统一工具执行方法
                    await ExecuteToolAsync(actualToolName);
                }
                else
                {
                    AntdUI.Message.warn(this, $"未找到对应的工具: {toolName}", null, 1);
                }
            }
            catch (Exception ex)
            {
                AntdUI.Message.error(this, "启动工具失败", null, 1);
                MessageBox.Show(
                    $"启动 {toolName} 时发生错误：\n{ex.Message}",
                    "错误",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 高亮显示指定按钮
        /// </summary>
        /// <param name="button">要高亮的按钮</param>
        private void HighlightButton(AntdUI.Button button)
        {
            if (button == null) return;

            try
            {
                // 临时改变按钮背景色以实现高亮效果
                var originalBackColor = button.BackColor;
                button.BackColor = System.Drawing.Color.LightBlue;

                // 使用定时器恢复原始颜色
                var timer = new System.Windows.Forms.Timer();
                timer.Interval = 1000; // 1秒后恢复
                timer.Tick += (s, e) =>
                {
                    button.BackColor = originalBackColor;
                    timer.Stop();
                    timer.Dispose();
                };
                timer.Start();

                // 确保按钮可见（滚动到按钮位置）
                button.Focus();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"高亮按钮失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 自定义绘制 TreeView 节点
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">绘制事件参数</param>
        private void ToolsTreeView_DrawNode(object sender, DrawTreeNodeEventArgs e)
        {
            try
            {
                // 确保绘制区域有效
                if (e.Bounds.Width <= 0 || e.Bounds.Height <= 0)
                {
                    e.DrawDefault = true;
                    return;
                }

                // 设置背景色
                Color backColor;
                Color textColor;

                if (e.Node.IsSelected)
                {
                    // 选中状态
                    backColor = Color.FromArgb(52, 144, 220);
                    textColor = Color.White;
                }
                else if ((e.State & TreeNodeStates.Hot) == TreeNodeStates.Hot)
                {
                    // 悬停状态
                    backColor = Color.FromArgb(230, 240, 250);
                    textColor = Color.FromArgb(52, 73, 94);
                }
                else
                {
                    // 默认状态
                    backColor = Color.FromArgb(248, 249, 250);
                    textColor = Color.FromArgb(52, 73, 94);
                }

                // 绘制背景
                using (SolidBrush backBrush = new SolidBrush(backColor))
                {
                    e.Graphics.FillRectangle(backBrush, e.Bounds);
                }

                // 绘制文本
                using (SolidBrush textBrush = new SolidBrush(textColor))
                {
                    // 使用支持 Emoji 的字体
                    Font nodeFont;
                    try
                    {
                        // 优先使用 Segoe UI Emoji 字体
                        nodeFont = new Font("Segoe UI Emoji", 9f, FontStyle.Regular);
                    }
                    catch
                    {
                        // 如果 Segoe UI Emoji 不可用，使用 Microsoft YaHei UI
                        try
                        {
                            nodeFont = new Font("Microsoft YaHei UI", 9f, FontStyle.Regular);
                        }
                        catch
                        {
                            // 最后回退到默认字体
                            nodeFont = e.Node.NodeFont ?? toolsTreeView.Font;
                        }
                    }

                    // 为分类节点使用稍大的字体
                    if (e.Node.Nodes.Count > 0 || e.Node.Tag?.ToString() == "显示全部")
                    {
                        try
                        {
                            nodeFont = new Font(nodeFont.FontFamily, nodeFont.Size + 0.5f, FontStyle.Bold);
                        }
                        catch
                        {
                            // 如果创建粗体字体失败，使用原字体
                        }
                    }

                    Rectangle textBounds = new Rectangle(
                        e.Bounds.X + 5,
                        e.Bounds.Y + 2,
                        Math.Max(1, e.Bounds.Width - 10),
                        Math.Max(1, e.Bounds.Height - 4));

                    StringFormat stringFormat = new StringFormat
                    {
                        LineAlignment = StringAlignment.Center,
                        Alignment = StringAlignment.Near,
                        Trimming = StringTrimming.None,
                        FormatFlags = StringFormatFlags.NoWrap
                    };

                    e.Graphics.DrawString(e.Node.Text, nodeFont, textBrush, textBounds, stringFormat);
                }

                // 绘制焦点框（如果需要）
                if ((e.State & TreeNodeStates.Focused) == TreeNodeStates.Focused)
                {
                    using (Pen focusPen = new Pen(Color.FromArgb(52, 144, 220), 1))
                    {
                        focusPen.DashStyle = System.Drawing.Drawing2D.DashStyle.Dot;
                        Rectangle focusRect = new Rectangle(e.Bounds.X + 1, e.Bounds.Y + 1,
                            e.Bounds.Width - 2, e.Bounds.Height - 2);
                        e.Graphics.DrawRectangle(focusPen, focusRect);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"绘制TreeView节点失败: {ex.Message}");
                // 如果自定义绘制失败，使用默认绘制
                e.DrawDefault = true;
            }
        }

        /// <summary>
        /// 处理"显示全部"节点点击事件
        /// </summary>
        private void Handle显示全部Click()
        {
            try
            {
                // 显示所有按钮
                显示全部Buttons();

                // 重置滚动位置到顶部
                scrollPanel.AutoScrollPosition = new Point(0, 0);
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"显示所有工具时发生错误：\n{ex.Message}",
                    "错误",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 处理分类节点点击事件
        /// </summary>
        /// <param name="categoryName">分类名称</param>
        private void HandleCategoryClick(string categoryName)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"点击分类: {categoryName}");

                // 根据分类名称过滤显示按钮
                FilterButtonsByCategory(categoryName);

                // 重置滚动位置到顶部
                scrollPanel.AutoScrollPosition = new Point(0, 0);

                // 显示成功提示
                AntdUI.Message.info(this, $"已切换到 {categoryName} 分类", null, 1);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"处理分类点击失败: {ex.Message}");
                MessageBox.Show(
                    $"过滤 {categoryName} 时发生错误：\n{ex.Message}",
                    "错误",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 根据分类过滤显示按钮
        /// </summary>
        /// <param name="categoryName">分类名称</param>
        private void FilterButtonsByCategory(string categoryName)
        {
            // 暂停布局更新以提高性能
            tableLayoutPanel1.SuspendLayout();

            try
            {
                // 定义分类与按钮的映射关系（支持 Tag 和文本两种方式）
                var categoryButtonMapping = new Dictionary<string, List<AntdUI.Button>>
                {
                    // 使用 Tag 方式
                    {
                        "AI工具",
                        new List<AntdUI.Button> { btnDeepSeek, btnChatGPT, btnPPTAssistant, btnXiaoHuanXiong, btnDigitalHuman, btnTextToImage, btnTextToVideo, btnTextToVoice, btnTextToPPT }
                    },
                    {
                        "系统工具",
                        new List<AntdUI.Button> { btnReleaseMemory, btnChangePassword, btnIPInfo, btnActivationTool }
                    },
                    {
                        "网络工具",
                        new List<AntdUI.Button> { btnNvtOa, btnBaidu, btnFileConvert }
                    },
                    {
                        "实用工具",
                        new List<AntdUI.Button> { btnSearchFiles, btnMindMap, btnPrivacyLock }
                    },
                    // 使用文本方式（向后兼容）
                    {
                        "🤖 AI工具",
                        new List<AntdUI.Button> { btnDeepSeek, btnChatGPT, btnPPTAssistant, btnXiaoHuanXiong, btnDigitalHuman, btnTextToImage, btnTextToVideo, btnTextToVoice, btnTextToPPT }
                    },
                    {
                        "⚙️ 系统工具",
                        new List<AntdUI.Button> { btnReleaseMemory, btnChangePassword, btnIPInfo, btnActivationTool }
                    },
                    {
                        "🌍 网络工具",
                        new List<AntdUI.Button> { btnNvtOa, btnBaidu, btnFileConvert }
                    },
                    {
                        "🛠️ 实用工具",
                        new List<AntdUI.Button> { btnSearchFiles, btnMindMap, btnPrivacyLock }
                    }
                };

                if (categoryButtonMapping.ContainsKey(categoryName))
                {
                    System.Diagnostics.Debug.WriteLine($"找到分类映射: {categoryName}");

                    // 获取当前分类的按钮列表
                    var categoryButtons = categoryButtonMapping[categoryName];
                    System.Diagnostics.Debug.WriteLine($"该分类包含 {categoryButtons.Count} 个按钮");

                    // 首先隐藏所有按钮
                    foreach (Control control in tableLayoutPanel1.Controls)
                    {
                        if (control is AntdUI.Button)
                        {
                            control.Visible = false;
                        }
                    }

                    // 重新排列并显示分类按钮
                    RearrangeButtons(categoryButtons);
                    System.Diagnostics.Debug.WriteLine("按钮重新排列完成");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"未找到分类映射: {categoryName}，显示所有按钮");
                    // 如果分类名称不匹配，显示所有按钮
                    显示全部Buttons();
                }
            }
            finally
            {
                // 恢复布局更新
                tableLayoutPanel1.ResumeLayout(true);
            }
        }

        /// <summary>
        /// 重新排列按钮位置，使其紧凑显示
        /// </summary>
        /// <param name="buttonsToShow">要显示的按钮列表</param>
        private void RearrangeButtons(List<AntdUI.Button> buttonsToShow)
        {
            int columns = tableLayoutPanel1.ColumnCount; // 4列
            int currentIndex = 0;

            foreach (var button in buttonsToShow)
            {
                if (button != null)
                {
                    // 计算新的行列位置
                    int row = currentIndex / columns;
                    int col = currentIndex % columns;

                    // 从原位置移除按钮
                    tableLayoutPanel1.Controls.Remove(button);

                    // 添加到新位置并设置为可见
                    tableLayoutPanel1.Controls.Add(button, col, row);
                    button.Visible = true;

                    currentIndex++;
                }
            }
        }

        /// <summary>
        /// 显示所有按钮并恢复原始布局
        /// </summary>
        private void 显示全部Buttons()
        {
            // 暂停布局更新以提高性能
            tableLayoutPanel1.SuspendLayout();

            try
            {
                // 清空所有控件
                tableLayoutPanel1.Controls.Clear();

                // 确保有足够的行数（至少5行，索引0-4）
                if (tableLayoutPanel1.RowCount < 5)
                {
                    tableLayoutPanel1.RowCount = 5;
                }

                // 确保tableLayoutPanel1有足够的高度显示所有行
                tableLayoutPanel1.Height = Math.Max(500, tableLayoutPanel1.RowCount * 100);

                // 按原始顺序重新添加所有按钮
                tableLayoutPanel1.Controls.Add(btnReleaseMemory, 0, 0);
                tableLayoutPanel1.Controls.Add(btnChangePassword, 1, 0);
                tableLayoutPanel1.Controls.Add(btnNvtOa, 2, 0);
                tableLayoutPanel1.Controls.Add(btnDeepSeek, 3, 0);
                tableLayoutPanel1.Controls.Add(btnSearchFiles, 0, 1);
                tableLayoutPanel1.Controls.Add(btnPrivacyLock, 1, 1);
                tableLayoutPanel1.Controls.Add(btnMindMap, 2, 1);
                tableLayoutPanel1.Controls.Add(btnChatGPT, 3, 1);
                tableLayoutPanel1.Controls.Add(btnFileConvert, 0, 2);
                tableLayoutPanel1.Controls.Add(btnActivationTool, 1, 2);
                tableLayoutPanel1.Controls.Add(btnBaidu, 2, 2);
                tableLayoutPanel1.Controls.Add(btnIPInfo, 3, 2);
                tableLayoutPanel1.Controls.Add(btnPPTAssistant, 0, 3);
                tableLayoutPanel1.Controls.Add(btnXiaoHuanXiong, 1, 3);
                tableLayoutPanel1.Controls.Add(btnDigitalHuman, 2, 3);
                tableLayoutPanel1.Controls.Add(btnTextToImage, 3, 3);
                tableLayoutPanel1.Controls.Add(btnTextToVideo, 0, 4);
                tableLayoutPanel1.Controls.Add(btnTextToVoice, 1, 4);
                tableLayoutPanel1.Controls.Add(btnTextToPPT, 2, 4);

                // 设置所有按钮为可见
                foreach (Control control in tableLayoutPanel1.Controls)
                {
                    if (control is AntdUI.Button)
                    {
                        control.Visible = true;
                    }
                }
            }
            finally
            {
                // 恢复布局更新
                tableLayoutPanel1.ResumeLayout(true);
            }
        }

        /// <summary>
        /// 检查并修复 Emoji 显示问题
        /// </summary>
        private void CheckAndFixEmojiDisplay()
        {
            try
            {
                // 检查系统是否支持 Emoji 显示
                bool emojiSupported = IsFontSupported("Segoe UI Emoji") || IsFontSupported("Microsoft YaHei UI");

                if (!emojiSupported)
                {
                    // 如果不支持 Emoji，使用文本替代方案
                    ReplaceEmojiWithText();
                    System.Diagnostics.Debug.WriteLine("系统不支持 Emoji 字体，已切换到文本替代方案");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"检查 Emoji 支持时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查指定字体是否可用
        /// </summary>
        /// <param name="fontName">字体名称</param>
        /// <returns>字体是否可用</returns>
        private bool IsFontSupported(string fontName)
        {
            try
            {
                using (Font testFont = new Font(fontName, 9.5f))
                {
                    return testFont.Name.Equals(fontName, StringComparison.OrdinalIgnoreCase);
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 将 Emoji 替换为文本符号
        /// </summary>
        private void ReplaceEmojiWithText()
        {
            try
            {
                foreach (TreeNode node in toolsTreeView.Nodes)
                {
                    ReplaceNodeEmojiWithText(node);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"替换 Emoji 时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 递归替换节点中的 Emoji
        /// </summary>
        /// <param name="node">要处理的节点</param>
        private void ReplaceNodeEmojiWithText(TreeNode node)
        {
            // Emoji 到文本的映射
            var emojiToText = new Dictionary<string, string>
            {
                { "📋 显示全部", "[*] 显示全部" },
                { "🤖 AI工具", "[AI] AI工具" },
                { "⚙️ 系统工具", "[SYS] 系统工具" },
                { "🌍 网络工具", "[NET] 网络工具" },
                { "🛠️ 实用工具", "[TOOL] 实用工具" },
                { "  🧠 DeepSeek", "  • DeepSeek" },
                { "  💬 ChatGPT", "  • ChatGPT" },
                { "  📊 PPT助手", "  • PPT助手" },
                { "  🧹 释放系统内存", "  • 释放系统内存" },
                { "  🔐 修改计算机密码", "  • 修改计算机密码" },
                { "  🌐 IP信息查看", "  • IP信息查看" },
                { "  🔑 激活工具", "  • 激活工具" },
                { "  🏢 NVT-OA", "  • NVT-OA" },
                { "  🔍 百度", "  • 百度" },
                { "  🔄 文件转换", "  • 文件转换" },
                { "  📁 搜索文件", "  • 搜索文件" },
                { "  🗺️ 思维导图", "  • 思维导图" },
                { "  📈 FineBI商业智能", "  • FineBI商业智能" }
            };

            // 替换当前节点的文本
            if (emojiToText.ContainsKey(node.Text))
            {
                node.Text = emojiToText[node.Text];
            }

            // 递归处理子节点
            foreach (TreeNode childNode in node.Nodes)
            {
                ReplaceNodeEmojiWithText(childNode);
            }
        }
    }
}
