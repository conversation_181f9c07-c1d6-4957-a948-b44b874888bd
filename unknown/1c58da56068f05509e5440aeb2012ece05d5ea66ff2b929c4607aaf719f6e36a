using System;
using System.Collections.Generic;
using 工具箱.Business.Tools.AI;
using 工具箱.Business.Tools.Network;
using 工具箱.Business.Tools.SystemTools;
using 工具箱.Business.Tools.Utility;
using 工具箱.Core.Interfaces;
using 工具箱.Infrastructure.ErrorHandling;
using 工具箱.Infrastructure.Services;

namespace 工具箱.Infrastructure.DependencyInjection
{
    /// <summary>
    /// 简单的服务容器实现
    /// </summary>
    public class ServiceContainer
    {
        private readonly Dictionary<Type, object> _singletonServices;
        private readonly Dictionary<Type, Func<object>> _transientFactories;
        private readonly object _lockObject = new object();

        public ServiceContainer()
        {
            _singletonServices = new Dictionary<Type, object>();
            _transientFactories = new Dictionary<Type, Func<object>>();
        }

        /// <summary>
        /// 注册单例服务
        /// </summary>
        /// <typeparam name="TInterface">接口类型</typeparam>
        /// <typeparam name="TImplementation">实现类型</typeparam>
        public void RegisterSingleton<TInterface, TImplementation>()
            where TImplementation : class, TInterface, new()
        {
            lock (_lockObject)
            {
                _transientFactories[typeof(TInterface)] = () =>
                {
                    if (!_singletonServices.TryGetValue(typeof(TInterface), out var instance))
                    {
                        instance = CreateInstance<TImplementation>();
                        _singletonServices[typeof(TInterface)] = instance;
                    }
                    return instance;
                };
            }
        }

        /// <summary>
        /// 注册单例服务实例
        /// </summary>
        /// <typeparam name="TInterface">接口类型</typeparam>
        /// <param name="instance">服务实例</param>
        public void RegisterSingleton<TInterface>(TInterface instance)
        {
            lock (_lockObject)
            {
                _singletonServices[typeof(TInterface)] = instance;
                _transientFactories[typeof(TInterface)] = () => instance;
            }
        }

        /// <summary>
        /// 注册瞬态服务
        /// </summary>
        /// <typeparam name="TInterface">接口类型</typeparam>
        /// <typeparam name="TImplementation">实现类型</typeparam>
        public void RegisterTransient<TInterface, TImplementation>()
            where TImplementation : class, TInterface, new()
        {
            lock (_lockObject)
            {
                _transientFactories[typeof(TInterface)] = () => CreateInstance<TImplementation>();
            }
        }

        /// <summary>
        /// 注册瞬态服务工厂
        /// </summary>
        /// <typeparam name="TInterface">接口类型</typeparam>
        /// <param name="factory">工厂方法</param>
        public void RegisterTransient<TInterface>(Func<TInterface> factory)
        {
            lock (_lockObject)
            {
                _transientFactories[typeof(TInterface)] = () => factory();
            }
        }

        /// <summary>
        /// 解析服务
        /// </summary>
        /// <typeparam name="T">服务类型</typeparam>
        /// <returns>服务实例</returns>
        public T Resolve<T>()
        {
            return (T)Resolve(typeof(T));
        }

        /// <summary>
        /// 解析服务
        /// </summary>
        /// <param name="serviceType">服务类型</param>
        /// <returns>服务实例</returns>
        public object Resolve(Type serviceType)
        {
            lock (_lockObject)
            {
                if (_transientFactories.TryGetValue(serviceType, out var factory))
                {
                    return factory();
                }

                throw new InvalidOperationException($"服务类型 {serviceType.Name} 未注册");
            }
        }

        /// <summary>
        /// 尝试解析服务
        /// </summary>
        /// <typeparam name="T">服务类型</typeparam>
        /// <param name="service">输出的服务实例</param>
        /// <returns>是否解析成功</returns>
        public bool TryResolve<T>(out T service)
        {
            try
            {
                service = Resolve<T>();
                return true;
            }
            catch
            {
                service = default(T);
                return false;
            }
        }

        /// <summary>
        /// 创建实例（支持构造函数注入）
        /// </summary>
        /// <typeparam name="T">类型</typeparam>
        /// <returns>实例</returns>
        private T CreateInstance<T>() where T : new()
        {
            var type = typeof(T);
            var constructors = type.GetConstructors();

            // 优先使用有参数的构造函数
            foreach (var constructor in constructors)
            {
                var parameters = constructor.GetParameters();
                if (parameters.Length > 0)
                {
                    try
                    {
                        var args = new object[parameters.Length];
                        for (int i = 0; i < parameters.Length; i++)
                        {
                            args[i] = Resolve(parameters[i].ParameterType);
                        }
                        return (T)Activator.CreateInstance(type, args);
                    }
                    catch
                    {
                        // 如果依赖解析失败，尝试下一个构造函数
                        continue;
                    }
                }
            }

            // 使用无参构造函数
            return new T();
        }
    }

    /// <summary>
    /// 服务配置扩展方法
    /// </summary>
    public static class ServiceContainerExtensions
    {
        /// <summary>
        /// 配置工具箱服务
        /// </summary>
        /// <param name="container">服务容器</param>
        /// <returns>配置后的服务容器</returns>
        public static ServiceContainer ConfigureToolboxServices(this ServiceContainer container)
        {
            // 注册核心服务（使用空实现）
            var configService = new NullConfigurationService();
            var loggingService = new NullLoggingService();
            var toolManager = new ToolManager(loggingService);
            var exceptionHandler = new GlobalExceptionHandler(loggingService, configService);

            container.RegisterSingleton<IConfigurationService>(configService);
            container.RegisterSingleton<ILoggingService>(loggingService);
            container.RegisterSingleton<IToolManager>(toolManager);
            container.RegisterSingleton<GlobalExceptionHandler>(exceptionHandler);

            // 注意：工具注册将在InitializeToolManager方法中进行

            return container;
        }

        /// <summary>
        /// 初始化工具管理器
        /// </summary>
        /// <param name="container">服务容器</param>
        /// <returns>配置后的服务容器</returns>
        public static ServiceContainer InitializeToolManager(this ServiceContainer container)
        {
            var toolManager = container.Resolve<IToolManager>();
            var logger = container.Resolve<ILoggingService>();
            var configService = container.Resolve<IConfigurationService>();

            try
            {
                // 注册AI工具
                toolManager.RegisterTool(new ChatGPTTool(logger, configService));
                toolManager.RegisterTool(new DeepSeekTool(logger, configService));
                toolManager.RegisterTool(new PPTAssistantTool(logger, configService));
                toolManager.RegisterTool(new XiaoHuanXiongTool(logger, configService));
                toolManager.RegisterTool(new DigitalHumanTool(logger, configService));
                toolManager.RegisterTool(new TextToImageTool(logger, configService));
                toolManager.RegisterTool(new TextToVideoTool(logger, configService));
                toolManager.RegisterTool(new TextToVoiceTool(logger, configService));
                toolManager.RegisterTool(new TextToPPTTool(logger, configService));

                // 注册系统工具
                toolManager.RegisterTool(new SystemCleanerTool(logger, configService));
                toolManager.RegisterTool(new ChangePasswordTool(logger, configService));
                toolManager.RegisterTool(new IPInfoTool(logger, configService));
                toolManager.RegisterTool(new ActivationTool(logger, configService));

                // 注册网络工具
                toolManager.RegisterTool(new NvtOaTool(logger, configService));
                toolManager.RegisterTool(new BaiduTool(logger, configService));
                toolManager.RegisterTool(new FileConvertTool(logger, configService));

                // 注册实用工具
                toolManager.RegisterTool(new FileSearchTool(logger, configService));
                toolManager.RegisterTool(new MindMapTool(logger, configService));
                toolManager.RegisterTool(new FineBITool(logger, configService));
            }
            catch (Exception ex)
            {
                logger?.LogError(ex, "初始化工具管理器失败");
            }

            return container;
        }
    }
}
