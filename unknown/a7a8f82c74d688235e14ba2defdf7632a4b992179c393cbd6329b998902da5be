# 工具箱项目结构文档

## 📋 项目概述

**工具箱**是一个基于Windows Forms的多功能工具集合应用程序，采用分层架构设计，集成了AI工具、系统工具、网络工具和实用工具等多种功能模块。

- **项目名称**: 工具箱
- **目标框架**: .NET Framework 4.5.2
- **应用类型**: Windows Forms 桌面应用程序
- **架构模式**: 分层架构 + 依赖注入
- **开发语言**: C#

## 🛠️ 技术栈

### 核心框架
- **.NET Framework 4.5.2** - 主要运行时框架
- **Windows Forms** - 用户界面框架
- **System.Management** - 系统管理功能

### 第三方库
- **AntdUI** - 现代化UI组件库
- **Newtonsoft.Json 13.0.3** - JSON序列化/反序列化
- **IrisSkin4** - 界面皮肤框架

### 开发工具
- **Visual Studio** - 主要开发环境
- **MSBuild 15.0** - 项目构建工具

## 🏗️ 架构设计

项目采用经典的分层架构模式，遵循关注点分离原则：

```
┌─────────────────────────────────────┐
│           Presentation Layer        │  ← UI层：Forms, Services
├─────────────────────────────────────┤
│            Business Layer           │  ← 业务层：Tools, Logic
├─────────────────────────────────────┤
│         Infrastructure Layer        │  ← 基础设施层：Services, DI
├─────────────────────────────────────┤
│              Core Layer             │  ← 核心层：Interfaces, Models
└─────────────────────────────────────┘
```

### 设计原则
- **依赖倒置** - 通过接口定义契约
- **单一职责** - 每个类专注单一功能
- **开闭原则** - 易于扩展新工具
- **依赖注入** - 松耦合的组件关系

## 📁 项目目录结构

```
工具箱/
├── 📁 Business/                          # 业务逻辑层
│   └── 📁 Tools/                         # 工具实现
│       ├── 📁 AI/                        # AI相关工具
│       │   └── 📄 ChatGPTTool.cs         # ChatGPT等AI工具集合
│       ├── 📁 Network/                   # 网络工具
│       │   └── 📄 NetworkTools.cs        # 网络相关工具
│       ├── 📁 System/                    # 系统工具
│       │   └── 📄 SystemCleanerTool.cs   # 系统清理工具
│       └── 📁 Utility/                   # 实用工具
│           └── 📄 UtilityTools.cs        # 文件搜索等实用工具
│
├── 📁 Core/                              # 核心层
│   ├── 📁 Interfaces/                    # 接口定义
│   │   ├── 📄 IConfigurationService.cs   # 配置服务接口
│   │   ├── 📄 ILoggingService.cs         # 日志服务接口
│   │   ├── 📄 ITool.cs                   # 工具基础接口
│   │   └── 📄 IToolManager.cs            # 工具管理器接口
│   └── 📁 Models/                        # 数据模型
│       ├── 📄 ApplicationConfiguration.cs # 应用配置模型
│       ├── 📄 BaseTool.cs                # 工具基础类
│       └── 📄 ToolConfiguration.cs       # 工具配置模型
│
├── 📁 Infrastructure/                     # 基础设施层
│   ├── 📁 DependencyInjection/           # 依赖注入
│   │   └── 📄 ServiceContainer.cs        # 服务容器
│   ├── 📁 ErrorHandling/                 # 错误处理
│   │   ├── 📄 ErrorModels.cs             # 错误模型定义
│   │   └── 📄 GlobalExceptionHandler.cs  # 全局异常处理器
│   └── 📁 Services/                      # 基础服务
│       ├── 📄 NullConfigurationService.cs # 空配置服务实现
│       ├── 📄 NullLoggingService.cs      # 空日志服务实现
│       └── 📄 ToolManager.cs             # 工具管理器实现
│
├── 📁 Presentation/                       # 表示层
│   └── 📁 Services/                      # 表示层服务
│       └── 📄 ApplicationService.cs      # 应用程序服务
│
├── 📁 Properties/                         # 项目属性
│   ├── 📄 AssemblyInfo.cs                # 程序集信息
│   ├── 📄 Resources.Designer.cs          # 资源文件
│   ├── 📄 Resources.resx                 # 资源定义
│   ├── 📄 Settings.Designer.cs           # 设置文件
│   └── 📄 Settings.settings              # 应用设置
│
├── 📁 bin/Debug/                         # 编译输出目录
│   ├── 📄 AntdUI.dll                     # UI组件库
│   ├── 📄 IrisSkin4.dll                  # 皮肤库
│   ├── 📄 Newtonsoft.Json.dll            # JSON库
│   ├── 📄 RealOne.ssk                    # 皮肤文件
│   ├── 📄 RealOne1.ssk                   # 皮肤文件
│   ├── 📄 工具箱.exe                      # 主程序
│   ├── 📄 工具箱.exe.config              # 配置文件
│   └── 📄 工具箱.pdb                      # 调试符号
│
├── 📁 obj/Debug/                         # 临时编译文件
│
├── 📄 App.config                         # 应用程序配置
├── 📄 CleanupProgressForm.cs             # 清理进度窗体
├── 📄 CleanupProgressForm.Designer.cs    # 清理进度窗体设计器
├── 📄 CleanupProgressForm.resx           # 清理进度窗体资源
├── 📄 FileSearchForm.cs                  # 文件搜索窗体
├── 📄 FileSearchForm.Designer.cs         # 文件搜索窗体设计器
├── 📄 FileSearchForm.resx                # 文件搜索窗体资源
├── 📄 Form1.cs                           # 主窗体
├── 📄 Form1.Designer.cs                  # 主窗体设计器
├── 📄 Form1.resx                         # 主窗体资源
├── 📄 NVT.ico                            # 应用程序图标
├── 📄 nuget.config                       # NuGet配置
├── 📄 packages.config                    # 包依赖配置
├── 📄 Program.cs                         # 程序入口点
├── 📄 SystemCleaner.cs                   # 系统清理核心类
└── 📄 工具箱.csproj                       # 项目文件
```

## 🔧 核心组件说明

### 接口层 (Core/Interfaces)

| 接口 | 描述 | 主要方法 |
|------|------|----------|
| `ITool` | 工具基础接口 | `ExecuteAsync()`, `ValidateConfiguration()` |
| `IToolManager` | 工具管理器接口 | `RegisterTool()`, `GetAllTools()`, `ExecuteToolAsync()` |
| `ILoggingService` | 日志服务接口 | `LogInfo()`, `LogError()`, `LogWarning()` |
| `IConfigurationService` | 配置服务接口 | `GetConfiguration()`, `SaveConfiguration()` |

### 模型层 (Core/Models)

| 类 | 描述 | 主要属性 |
|----|------|----------|
| `BaseTool` | 工具基础抽象类 | `Name`, `Description`, `Category`, `IsEnabled` |
| `ToolConfiguration` | 工具配置模型 | `ToolId`, `Settings`, `IsEnabled` |
| `ApplicationConfiguration` | 应用配置模型 | `Theme`, `Language`, `ToolConfigurations` |

### 服务层 (Infrastructure/Services)

| 服务 | 描述 | 功能 |
|------|------|------|
| `ToolManager` | 工具管理器 | 工具注册、发现、执行、验证 |
| `ServiceContainer` | 服务容器 | 依赖注入、服务生命周期管理 |
| `GlobalExceptionHandler` | 全局异常处理 | 异常捕获、日志记录、用户友好提示 |

## 🎯 业务工具分类

### 🤖 AI工具 (Business/Tools/AI)
- **ChatGPT工具** - AI对话和文本生成
- **DeepSeek工具** - 深度学习AI服务
- **PPT助手工具** - 演示文稿AI辅助
- **小幻熊工具** - AI创意助手
- **数字人工具** - 虚拟数字人生成
- **文本转图片工具** - AI图像生成
- **文本转视频工具** - AI视频生成

### ⚙️ 系统工具 (Business/Tools/System)
- **系统清理工具** - 临时文件清理、内存释放
- **性能监控** - 系统资源监控
- **进程管理** - 进程查看和管理

### 🌐 网络工具 (Business/Tools/Network)
- **NVT-OA工具** - 企业办公系统集成
- **百度搜索** - 快速搜索功能
- **文件转换** - 在线文件格式转换
- **网络诊断** - 网络连接测试

### 🔧 实用工具 (Business/Tools/Utility)
- **文件搜索工具** - 快速文件查找
- **批量重命名** - 文件批量操作
- **文本处理** - 文本编辑和格式化

## 🚀 构建和运行

### 系统要求
- **操作系统**: Windows 7 SP1 或更高版本
- **运行时**: .NET Framework 4.5.2 或更高版本
- **内存**: 最少 512MB RAM
- **磁盘空间**: 最少 100MB 可用空间

### 构建步骤
1. **克隆项目**
   ```bash
   git clone [repository-url]
   cd 工具箱
   ```

2. **还原NuGet包**
   ```bash
   nuget restore packages.config
   ```

3. **编译项目**
   ```bash
   msbuild 工具箱.csproj /p:Configuration=Release
   ```

4. **运行应用**
   ```bash
   cd bin\Release
   工具箱.exe
   ```

### 开发环境设置
1. 安装 Visual Studio 2017 或更高版本
2. 确保安装了 .NET Framework 4.5.2 开发包
3. 打开 `工具箱.csproj` 文件
4. 按 F5 开始调试

## 📝 扩展指南

### 添加新工具
1. 在 `Business/Tools/[Category]/` 下创建新的工具类
2. 继承 `BaseTool` 抽象类
3. 实现必要的接口方法
4. 在 `ServiceContainer.cs` 中注册新工具
5. 更新主界面的工具树结构

### 自定义配置
1. 修改 `ApplicationConfiguration.cs` 添加新配置项
2. 更新配置服务实现
3. 在UI中添加配置界面

### 错误处理
- 所有业务异常应继承 `BusinessException`
- 使用 `GlobalExceptionHandler` 进行统一处理
- 提供用户友好的错误信息

## 📄 许可证

Copyright © 2025 工具箱项目组

---

*最后更新: 2025年1月*