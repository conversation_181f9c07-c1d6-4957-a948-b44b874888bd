using System;
using System.Collections.Generic;
using System.Linq;
using 工具箱.Core.Interfaces;

namespace 工具箱.Infrastructure.Services
{
    /// <summary>
    /// 空配置服务实现，不执行任何实际的配置存储操作
    /// </summary>
    public class NullConfigurationService : IConfigurationService
    {
        private readonly Dictionary<string, object> _memoryConfig = new Dictionary<string, object>();

        public event EventHandler<ConfigurationChangedEventArgs> ConfigurationChanged;

        public T GetValue<T>(string key, T defaultValue = default(T))
        {
            if (string.IsNullOrWhiteSpace(key))
                return defaultValue;

            if (_memoryConfig.TryGetValue(key, out var value))
            {
                try
                {
                    if (value is T directValue)
                        return directValue;
                    
                    return (T)Convert.ChangeType(value, typeof(T));
                }
                catch
                {
                    return defaultValue;
                }
            }

            return defaultValue;
        }

        public void SetValue<T>(string key, T value)
        {
            if (string.IsNullOrWhiteSpace(key))
                return;

            var oldValue = _memoryConfig.TryGetValue(key, out var existing) ? existing : null;
            _memoryConfig[key] = value;

            // 触发配置变更事件（仅在内存中）
            ConfigurationChanged?.Invoke(this, new ConfigurationChangedEventArgs
            {
                Key = key,
                OldValue = oldValue,
                NewValue = value
            });
        }

        public bool HasKey(string key)
        {
            if (string.IsNullOrWhiteSpace(key))
                return false;

            return _memoryConfig.ContainsKey(key);
        }

        public bool RemoveKey(string key)
        {
            if (string.IsNullOrWhiteSpace(key))
                return false;

            if (_memoryConfig.TryGetValue(key, out var oldValue))
            {
                _memoryConfig.Remove(key);

                ConfigurationChanged?.Invoke(this, new ConfigurationChangedEventArgs
                {
                    Key = key,
                    OldValue = oldValue,
                    NewValue = null
                });

                return true;
            }

            return false;
        }

        public IEnumerable<string> GetAllKeys()
        {
            return _memoryConfig.Keys.ToArray();
        }

        public Dictionary<string, object> GetSection(string prefix)
        {
            if (string.IsNullOrWhiteSpace(prefix))
                return new Dictionary<string, object>();

            var section = new Dictionary<string, object>();
            var prefixWithDot = prefix.EndsWith(".") ? prefix : prefix + ".";

            foreach (var kvp in _memoryConfig)
            {
                if (kvp.Key.StartsWith(prefixWithDot, StringComparison.OrdinalIgnoreCase))
                {
                    var sectionKey = kvp.Key.Substring(prefixWithDot.Length);
                    section[sectionKey] = kvp.Value;
                }
            }

            return section;
        }

        public void Save()
        {
            // 空实现 - 不保存任何文件
        }

        public void Reload()
        {
            // 空实现 - 不从文件重新加载
        }

        public void Reset()
        {
            _memoryConfig.Clear();
        }
    }
}