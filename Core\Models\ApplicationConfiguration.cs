using System;
using System.Collections.Generic;
using 工具箱.Core.Interfaces;

namespace 工具箱.Core.Models
{
    /// <summary>
    /// 应用程序配置模型
    /// </summary>
    public class ApplicationConfiguration : IConfigurationSection
    {
        public string SectionName => "Application";

        /// <summary>
        /// UI配置
        /// </summary>
        public UIConfiguration UI { get; set; } = new UIConfiguration();

        /// <summary>
        /// 日志配置
        /// </summary>
        public LoggingConfiguration Logging { get; set; } = new LoggingConfiguration();

        /// <summary>
        /// 通知配置
        /// </summary>
        public NotificationConfiguration Notification { get; set; } = new NotificationConfiguration();

        /// <summary>
        /// 性能配置
        /// </summary>
        public PerformanceConfiguration Performance { get; set; } = new PerformanceConfiguration();

        public void LoadFrom(IConfigurationService configService)
        {
            UI.LoadFrom(configService);
            Logging.LoadFrom(configService);
            Notification.LoadFrom(configService);
            Performance.LoadFrom(configService);
        }

        public void SaveTo(IConfigurationService configService)
        {
            UI.SaveTo(configService);
            Logging.SaveTo(configService);
            Notification.SaveTo(configService);
            Performance.SaveTo(configService);
        }

        public ValidationResult Validate()
        {
            var errors = new List<string>();
            var warnings = new List<string>();

            var uiValidation = UI.Validate();
            if (!uiValidation.IsValid)
                errors.AddRange(uiValidation.Errors);
            warnings.AddRange(uiValidation.Warnings);

            var loggingValidation = Logging.Validate();
            if (!loggingValidation.IsValid)
                errors.AddRange(loggingValidation.Errors);
            warnings.AddRange(loggingValidation.Warnings);

            var notificationValidation = Notification.Validate();
            if (!notificationValidation.IsValid)
                errors.AddRange(notificationValidation.Errors);
            warnings.AddRange(notificationValidation.Warnings);

            var performanceValidation = Performance.Validate();
            if (!performanceValidation.IsValid)
                errors.AddRange(performanceValidation.Errors);
            warnings.AddRange(performanceValidation.Warnings);

            return new ValidationResult
            {
                IsValid = errors.Count == 0,
                Errors = errors.ToArray(),
                Warnings = warnings.ToArray()
            };
        }
    }

    /// <summary>
    /// UI配置
    /// </summary>
    public class UIConfiguration : IConfigurationSection
    {
        public string SectionName => "Application.UI";

        /// <summary>
        /// 主题文件
        /// </summary>
        public string Theme { get; set; } = "RealOne.ssk";

        /// <summary>
        /// 语言设置
        /// </summary>
        public string Language { get; set; } = "zh-CN";

        /// <summary>
        /// 窗口大小
        /// </summary>
        public WindowSize WindowSize { get; set; } = new WindowSize { Width = 1200, Height = 800 };

        /// <summary>
        /// 窗口位置
        /// </summary>
        public WindowPosition WindowPosition { get; set; } = new WindowPosition { X = -1, Y = -1 }; // -1表示居中

        /// <summary>
        /// 是否最大化启动
        /// </summary>
        public bool StartMaximized { get; set; } = false;

        /// <summary>
        /// 是否显示侧边栏
        /// </summary>
        public bool ShowSidebar { get; set; } = true;

        /// <summary>
        /// 侧边栏宽度
        /// </summary>
        public int SidebarWidth { get; set; } = 250;

        /// <summary>
        /// 字体设置
        /// </summary>
        public FontConfiguration Font { get; set; } = new FontConfiguration();

        public void LoadFrom(IConfigurationService configService)
        {
            Theme = configService.GetValue("Application.UI.Theme", Theme);
            Language = configService.GetValue("Application.UI.Language", Language);
            StartMaximized = configService.GetValue("Application.UI.StartMaximized", StartMaximized);
            ShowSidebar = configService.GetValue("Application.UI.ShowSidebar", ShowSidebar);
            SidebarWidth = configService.GetValue("Application.UI.SidebarWidth", SidebarWidth);

            WindowSize.Width = configService.GetValue("Application.UI.WindowSize.Width", WindowSize.Width);
            WindowSize.Height = configService.GetValue("Application.UI.WindowSize.Height", WindowSize.Height);
            WindowPosition.X = configService.GetValue("Application.UI.WindowPosition.X", WindowPosition.X);
            WindowPosition.Y = configService.GetValue("Application.UI.WindowPosition.Y", WindowPosition.Y);

            Font.LoadFrom(configService);
        }

        public void SaveTo(IConfigurationService configService)
        {
            configService.SetValue("Application.UI.Theme", Theme);
            configService.SetValue("Application.UI.Language", Language);
            configService.SetValue("Application.UI.StartMaximized", StartMaximized);
            configService.SetValue("Application.UI.ShowSidebar", ShowSidebar);
            configService.SetValue("Application.UI.SidebarWidth", SidebarWidth);

            configService.SetValue("Application.UI.WindowSize.Width", WindowSize.Width);
            configService.SetValue("Application.UI.WindowSize.Height", WindowSize.Height);
            configService.SetValue("Application.UI.WindowPosition.X", WindowPosition.X);
            configService.SetValue("Application.UI.WindowPosition.Y", WindowPosition.Y);

            Font.SaveTo(configService);
        }

        public ValidationResult Validate()
        {
            var errors = new List<string>();
            var warnings = new List<string>();

            if (WindowSize.Width < 800 || WindowSize.Height < 600)
            {
                warnings.Add("窗口大小可能过小，建议至少800x600");
            }

            if (SidebarWidth < 150 || SidebarWidth > 500)
            {
                warnings.Add("侧边栏宽度建议在150-500像素之间");
            }

            var fontValidation = Font.Validate();
            if (!fontValidation.IsValid)
                errors.AddRange(fontValidation.Errors);
            warnings.AddRange(fontValidation.Warnings);

            return new ValidationResult
            {
                IsValid = errors.Count == 0,
                Errors = errors.ToArray(),
                Warnings = warnings.ToArray()
            };
        }
    }

    /// <summary>
    /// 窗口大小
    /// </summary>
    public class WindowSize
    {
        public int Width { get; set; }
        public int Height { get; set; }
    }

    /// <summary>
    /// 窗口位置
    /// </summary>
    public class WindowPosition
    {
        public int X { get; set; }
        public int Y { get; set; }
    }

    /// <summary>
    /// 字体配置
    /// </summary>
    public class FontConfiguration : IConfigurationSection
    {
        public string SectionName => "Application.UI.Font";

        public string FontFamily { get; set; } = "Microsoft YaHei UI";
        public float FontSize { get; set; } = 9f;
        public bool SupportEmoji { get; set; } = true;
        public string EmojiFontFamily { get; set; } = "Segoe UI Emoji";

        public void LoadFrom(IConfigurationService configService)
        {
            FontFamily = configService.GetValue("Application.UI.Font.FontFamily", FontFamily);
            FontSize = configService.GetValue("Application.UI.Font.FontSize", FontSize);
            SupportEmoji = configService.GetValue("Application.UI.Font.SupportEmoji", SupportEmoji);
            EmojiFontFamily = configService.GetValue("Application.UI.Font.EmojiFontFamily", EmojiFontFamily);
        }

        public void SaveTo(IConfigurationService configService)
        {
            configService.SetValue("Application.UI.Font.FontFamily", FontFamily);
            configService.SetValue("Application.UI.Font.FontSize", FontSize);
            configService.SetValue("Application.UI.Font.SupportEmoji", SupportEmoji);
            configService.SetValue("Application.UI.Font.EmojiFontFamily", EmojiFontFamily);
        }

        public ValidationResult Validate()
        {
            var errors = new List<string>();
            var warnings = new List<string>();

            if (FontSize < 6 || FontSize > 72)
            {
                warnings.Add("字体大小建议在6-72之间");
            }

            if (string.IsNullOrWhiteSpace(FontFamily))
            {
                errors.Add("字体族不能为空");
            }

            return new ValidationResult
            {
                IsValid = errors.Count == 0,
                Errors = errors.ToArray(),
                Warnings = warnings.ToArray()
            };
        }
    }

    /// <summary>
    /// 日志配置
    /// </summary>
    public class LoggingConfiguration : IConfigurationSection
    {
        public string SectionName => "Application.Logging";

        public LogLevel LogLevel { get; set; } = LogLevel.Info;
        public string LogFilePath { get; set; } = "Logs\\toolbox.log";
        public int MaxLogFileSize { get; set; } = 10 * 1024 * 1024; // 10MB
        public int MaxLogFiles { get; set; } = 5;
        public bool EnableConsoleLogging { get; set; } = true;
        public bool EnableFileLogging { get; set; } = true;

        public void LoadFrom(IConfigurationService configService)
        {
            LogLevel = configService.GetValue("Application.Logging.LogLevel", LogLevel);
            LogFilePath = configService.GetValue("Application.Logging.LogFilePath", LogFilePath);
            MaxLogFileSize = configService.GetValue("Application.Logging.MaxLogFileSize", MaxLogFileSize);
            MaxLogFiles = configService.GetValue("Application.Logging.MaxLogFiles", MaxLogFiles);
            EnableConsoleLogging = configService.GetValue("Application.Logging.EnableConsoleLogging", EnableConsoleLogging);
            EnableFileLogging = configService.GetValue("Application.Logging.EnableFileLogging", EnableFileLogging);
        }

        public void SaveTo(IConfigurationService configService)
        {
            configService.SetValue("Application.Logging.LogLevel", LogLevel);
            configService.SetValue("Application.Logging.LogFilePath", LogFilePath);
            configService.SetValue("Application.Logging.MaxLogFileSize", MaxLogFileSize);
            configService.SetValue("Application.Logging.MaxLogFiles", MaxLogFiles);
            configService.SetValue("Application.Logging.EnableConsoleLogging", EnableConsoleLogging);
            configService.SetValue("Application.Logging.EnableFileLogging", EnableFileLogging);
        }

        public ValidationResult Validate()
        {
            var errors = new List<string>();
            var warnings = new List<string>();

            if (string.IsNullOrWhiteSpace(LogFilePath))
            {
                errors.Add("日志文件路径不能为空");
            }

            if (MaxLogFileSize < 1024 * 1024) // 1MB
            {
                warnings.Add("日志文件大小限制可能过小");
            }

            if (MaxLogFiles < 1)
            {
                errors.Add("最大日志文件数量必须大于0");
            }

            return new ValidationResult
            {
                IsValid = errors.Count == 0,
                Errors = errors.ToArray(),
                Warnings = warnings.ToArray()
            };
        }
    }

    /// <summary>
    /// 通知配置
    /// </summary>
    public class NotificationConfiguration : IConfigurationSection
    {
        public string SectionName => "Application.Notification";

        public string NotificationFilePath { get; set; } = @"\\t-018254\临时文件中转\5555\11.txt";
        public int RefreshInterval { get; set; } = 300; // 5分钟
        public bool EnableNotifications { get; set; } = true;
        public bool ShowNotificationOnStartup { get; set; } = true;

        public void LoadFrom(IConfigurationService configService)
        {
            NotificationFilePath = configService.GetValue("Application.Notification.NotificationFilePath", NotificationFilePath);
            RefreshInterval = configService.GetValue("Application.Notification.RefreshInterval", RefreshInterval);
            EnableNotifications = configService.GetValue("Application.Notification.EnableNotifications", EnableNotifications);
            ShowNotificationOnStartup = configService.GetValue("Application.Notification.ShowNotificationOnStartup", ShowNotificationOnStartup);
        }

        public void SaveTo(IConfigurationService configService)
        {
            configService.SetValue("Application.Notification.NotificationFilePath", NotificationFilePath);
            configService.SetValue("Application.Notification.RefreshInterval", RefreshInterval);
            configService.SetValue("Application.Notification.EnableNotifications", EnableNotifications);
            configService.SetValue("Application.Notification.ShowNotificationOnStartup", ShowNotificationOnStartup);
        }

        public ValidationResult Validate()
        {
            var errors = new List<string>();
            var warnings = new List<string>();

            if (RefreshInterval < 30)
            {
                warnings.Add("通知刷新间隔可能过短，建议至少30秒");
            }

            return new ValidationResult
            {
                IsValid = errors.Count == 0,
                Errors = errors.ToArray(),
                Warnings = warnings.ToArray()
            };
        }
    }

    /// <summary>
    /// 性能配置
    /// </summary>
    public class PerformanceConfiguration : IConfigurationSection
    {
        public string SectionName => "Application.Performance";

        public int MaxConcurrentTools { get; set; } = 3;
        public int ToolExecutionTimeout { get; set; } = 30000; // 30秒
        public bool EnablePerformanceMonitoring { get; set; } = false;
        public bool EnableMemoryOptimization { get; set; } = true;

        public void LoadFrom(IConfigurationService configService)
        {
            MaxConcurrentTools = configService.GetValue("Application.Performance.MaxConcurrentTools", MaxConcurrentTools);
            ToolExecutionTimeout = configService.GetValue("Application.Performance.ToolExecutionTimeout", ToolExecutionTimeout);
            EnablePerformanceMonitoring = configService.GetValue("Application.Performance.EnablePerformanceMonitoring", EnablePerformanceMonitoring);
            EnableMemoryOptimization = configService.GetValue("Application.Performance.EnableMemoryOptimization", EnableMemoryOptimization);
        }

        public void SaveTo(IConfigurationService configService)
        {
            configService.SetValue("Application.Performance.MaxConcurrentTools", MaxConcurrentTools);
            configService.SetValue("Application.Performance.ToolExecutionTimeout", ToolExecutionTimeout);
            configService.SetValue("Application.Performance.EnablePerformanceMonitoring", EnablePerformanceMonitoring);
            configService.SetValue("Application.Performance.EnableMemoryOptimization", EnableMemoryOptimization);
        }

        public ValidationResult Validate()
        {
            var errors = new List<string>();
            var warnings = new List<string>();

            if (MaxConcurrentTools < 1)
            {
                errors.Add("最大并发工具数量必须大于0");
            }

            if (ToolExecutionTimeout < 1000)
            {
                warnings.Add("工具执行超时时间可能过短");
            }

            return new ValidationResult
            {
                IsValid = errors.Count == 0,
                Errors = errors.ToArray(),
                Warnings = warnings.ToArray()
            };
        }
    }
}
