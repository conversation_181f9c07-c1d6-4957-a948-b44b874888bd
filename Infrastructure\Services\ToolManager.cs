using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using 工具箱.Core.Interfaces;

namespace 工具箱.Infrastructure.Services
{
    /// <summary>
    /// 工具管理器实现
    /// </summary>
    public class ToolManager : IToolManager
    {
        private readonly Dictionary<string, ITool> _tools;
        private readonly ILoggingService _logger;
        private readonly object _lockObject = new object();

        public event EventHandler<ToolExecutionEventArgs> ToolExecuting;
        public event EventHandler<ToolExecutionEventArgs> ToolExecuted;
        public event EventHandler<ToolRegistrationEventArgs> ToolRegistered;
        public event EventHandler<ToolRegistrationEventArgs> ToolUnregistered;

        public ToolManager() : this(null)
        {
        }

        public ToolManager(ILoggingService logger)
        {
            _logger = logger;
            _tools = new Dictionary<string, ITool>(StringComparer.OrdinalIgnoreCase);
        }

        public IEnumerable<ITool> GetAllTools()
        {
            lock (_lockObject)
            {
                return _tools.Values.ToArray();
            }
        }

        public IEnumerable<ITool> GetToolsByCategory(ToolCategory category)
        {
            lock (_lockObject)
            {
                return _tools.Values.Where(t => t.Category == category).ToArray();
            }
        }

        public ITool GetToolByName(string name)
        {
            if (string.IsNullOrWhiteSpace(name))
                return null;

            lock (_lockObject)
            {
                _tools.TryGetValue(name, out var tool);
                return tool;
            }
        }

        public void RegisterTool(ITool tool)
        {
            if (tool == null)
                throw new ArgumentNullException(nameof(tool));

            if (string.IsNullOrWhiteSpace(tool.Name))
                throw new ArgumentException("工具名称不能为空", nameof(tool));

            lock (_lockObject)
            {
                if (_tools.ContainsKey(tool.Name))
                {
                    _logger?.LogWarning($"工具 {tool.Name} 已存在，将被替换");
                }

                _tools[tool.Name] = tool;
                _logger?.LogInfo($"工具 {tool.Name} 已注册，分类: {tool.Category}");

                // 触发注册事件
                ToolRegistered?.Invoke(this, new ToolRegistrationEventArgs
                {
                    ToolName = tool.Name,
                    Category = tool.Category
                });
            }
        }

        public void RegisterTool<T>() where T : ITool, new()
        {
            var tool = new T();
            RegisterTool(tool);
        }

        public bool UnregisterTool(string name)
        {
            if (string.IsNullOrWhiteSpace(name))
                return false;

            lock (_lockObject)
            {
                if (_tools.TryGetValue(name, out var tool))
                {
                    _tools.Remove(name);
                    _logger?.LogInfo($"工具 {name} 已取消注册");

                    // 触发取消注册事件
                    ToolUnregistered?.Invoke(this, new ToolRegistrationEventArgs
                    {
                        ToolName = name,
                        Category = tool.Category
                    });

                    return true;
                }

                return false;
            }
        }

        public async Task<ToolResult> ExecuteToolAsync(string toolName, ToolParameters parameters = null)
        {
            if (string.IsNullOrWhiteSpace(toolName))
            {
                return ToolResult.CreateFailure("工具名称不能为空");
            }

            ITool tool;
            lock (_lockObject)
            {
                if (!_tools.TryGetValue(toolName, out tool))
                {
                    return ToolResult.CreateFailure($"未找到工具: {toolName}");
                }
            }

            var executionArgs = new ToolExecutionEventArgs
            {
                ToolName = toolName,
                Parameters = parameters,
                StartTime = DateTime.Now
            };

            try
            {
                // 触发执行前事件
                ToolExecuting?.Invoke(this, executionArgs);

                _logger?.LogInfo($"开始执行工具: {toolName}");

                // 执行工具
                var result = await tool.ExecuteAsync(parameters);

                executionArgs.EndTime = DateTime.Now;
                executionArgs.Result = result;

                // 触发执行后事件
                ToolExecuted?.Invoke(this, executionArgs);

                if (result.Success)
                {
                    _logger?.LogInfo($"工具 {toolName} 执行成功: {result.Message}");
                }
                else
                {
                    _logger?.LogError($"工具 {toolName} 执行失败: {result.Message}");
                }

                return result;
            }
            catch (Exception ex)
            {
                executionArgs.EndTime = DateTime.Now;
                executionArgs.Result = ToolResult.CreateFailure($"工具执行异常: {ex.Message}", ex);

                // 触发执行后事件
                ToolExecuted?.Invoke(this, executionArgs);

                _logger?.LogError(ex, $"工具 {toolName} 执行异常");
                return ToolResult.CreateFailure($"工具执行异常: {ex.Message}", ex);
            }
        }

        public IEnumerable<ITool> SearchTools(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return Enumerable.Empty<ITool>();

            lock (_lockObject)
            {
                return _tools.Values.Where(tool =>
                    tool.Name.IndexOf(searchTerm, StringComparison.OrdinalIgnoreCase) >= 0 ||
                    tool.Description.IndexOf(searchTerm, StringComparison.OrdinalIgnoreCase) >= 0
                ).ToArray();
            }
        }

        public Dictionary<string, ValidationResult> ValidateAllTools()
        {
            var results = new Dictionary<string, ValidationResult>();

            lock (_lockObject)
            {
                foreach (var tool in _tools.Values)
                {
                    try
                    {
                        var validation = tool.ValidateConfiguration();
                        results[tool.Name] = validation;

                        if (!validation.IsValid)
                        {
                            _logger?.LogWarning($"工具 {tool.Name} 配置验证失败: {string.Join(", ", validation.Errors)}");
                        }

                        if (validation.Warnings?.Length > 0)
                        {
                            _logger?.LogWarning($"工具 {tool.Name} 配置警告: {string.Join(", ", validation.Warnings)}");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, $"验证工具 {tool.Name} 配置时发生异常");
                        results[tool.Name] = ValidationResult.Invalid($"验证异常: {ex.Message}");
                    }
                }
            }

            return results;
        }

        /// <summary>
        /// 获取工具统计信息
        /// </summary>
        /// <returns>工具统计信息</returns>
        public ToolStatistics GetStatistics()
        {
            lock (_lockObject)
            {
                var stats = new ToolStatistics
                {
                    TotalTools = _tools.Count,
                    EnabledTools = _tools.Values.Count(t => t.IsEnabled),
                    DisabledTools = _tools.Values.Count(t => !t.IsEnabled)
                };

                foreach (ToolCategory category in Enum.GetValues(typeof(ToolCategory)))
                {
                    var count = _tools.Values.Count(t => t.Category == category);
                    stats.ToolsByCategory[category] = count;
                }

                return stats;
            }
        }

        /// <summary>
        /// 批量注册工具
        /// </summary>
        /// <param name="tools">要注册的工具集合</param>
        public void RegisterTools(IEnumerable<ITool> tools)
        {
            if (tools == null)
                throw new ArgumentNullException(nameof(tools));

            foreach (var tool in tools)
            {
                RegisterTool(tool);
            }
        }

        /// <summary>
        /// 清空所有工具
        /// </summary>
        public void ClearAllTools()
        {
            lock (_lockObject)
            {
                var toolNames = _tools.Keys.ToArray();
                _tools.Clear();

                _logger?.LogInfo($"已清空所有工具，共 {toolNames.Length} 个");

                // 触发取消注册事件
                foreach (var toolName in toolNames)
                {
                    ToolUnregistered?.Invoke(this, new ToolRegistrationEventArgs
                    {
                        ToolName = toolName,
                        Category = ToolCategory.其他
                    });
                }
            }
        }
    }

    /// <summary>
    /// 工具统计信息
    /// </summary>
    public class ToolStatistics
    {
        public int TotalTools { get; set; }
        public int EnabledTools { get; set; }
        public int DisabledTools { get; set; }
        public Dictionary<ToolCategory, int> ToolsByCategory { get; set; } = new Dictionary<ToolCategory, int>();
    }
}
